import { useState, useRef } from "react";
import { PageHeader } from "@/components/PageHeader";
import StatCard from "@/components/StatCard";
import { DataTable, Column } from "@/components/DataTable";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import {
  Pencil,
  Trash2,
  FilterIcon,
  Plus,
  Calendar as CalendarIcon,
  FileUp,
  Eye,
  FileText,
  X
} from "lucide-react";
import { format, parseISO } from "date-fns";
import { pt } from "date-fns/locale";
import "@/components/ui/date-button.css";
import { cn } from "@/lib/utils";
import { useTransactions, Transaction } from "@/hooks/useTransactions";
import { useCategories } from "@/hooks/useCategories";
import { useSubcategories } from "@/hooks/useSubcategories";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { toastUtils } from "@/lib/toast-utils";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form";

// Schema de validação para o formulário de transação
const transactionSchema = z.object({
  description: z.string().min(1, "A descrição é obrigatória"),
  amount: z.number().min(0.01, "O valor deve ser maior que zero"),
  date: z.date({
    required_error: "A data é obrigatória",
  }),
  type: z.enum(["entrada", "saida"], {
    required_error: "O tipo é obrigatório",
  }),
  category_id: z.string().optional(),
  subcategory_id: z.string().optional(),
  comprovativo_url: z.string().optional()
});

// Component to render a red asterisk for required fields
const RequiredFieldMarker = () => (
  <span className="text-destructive ml-1">*</span>
);

const FluxoDeCaixa = () => {
  const [isNewTransactionOpen, setIsNewTransactionOpen] = useState(false);
  const [isViewComprovativoOpen, setIsViewComprovativoOpen] = useState(false);
  const [isEditTransactionOpen, setIsEditTransactionOpen] = useState(false);
  const [isDeleteTransactionOpen, setIsDeleteTransactionOpen] = useState(false);
  const [selectedTransaction, setSelectedTransaction] = useState<Transaction | null>(null);
  const [dateFrom, setDateFrom] = useState<Date>();
  const [dateTo, setDateTo] = useState<Date>();
  const [tipo, setTipo] = useState<string>("todos");
  const [categoria, setCategoria] = useState<string>("todas");
  const [subcategoria, setSubcategoria] = useState<string>("todas");
  const [searchQuery, setSearchQuery] = useState("");
  const [currentComprovativoUrl, setCurrentComprovativoUrl] = useState("");

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(25);

  const fileInputRef = useRef<HTMLInputElement>(null);
  const { categories } = useCategories();
  const { subcategories } = useSubcategories();

  // Prepare filters for API
  const filters = {
    search: searchQuery || undefined,
    type: tipo !== "todos" ? tipo : undefined,
    category_id: categoria !== "todas" ? categoria : undefined,
    subcategory_id: subcategoria !== "todas" ? subcategoria : undefined,
    start_date: dateFrom ? dateFrom.toISOString().split('T')[0] : undefined,
    end_date: dateTo ? dateTo.toISOString().split('T')[0] : undefined,
  };

  const pagination = {
    page: currentPage,
    limit: pageSize,
  };

  const {
    transactions = [],
    pagination: paginationData,
    isLoading,
    createTransaction,
    updateTransaction,
    deleteTransaction,
    removeFile,
    prepareFileForUpload,
    uploadComprovativo,
    clearSelectedFile,
    selectedFile,
    isUploading
  } = useTransactions(filters, pagination, {
    onUpdateSuccess: () => {
      // Fechar modal e resetar formulário apenas no sucesso
      setIsEditTransactionOpen(false);
      form.reset();
      clearSelectedFile();
    },
    onUpdateError: () => {
      // Manter modal aberto em caso de erro para o usuário tentar novamente
      console.log("Erro na atualização - modal permanece aberto");
    }
  });

  const form = useForm<z.infer<typeof transactionSchema>>({
    resolver: zodResolver(transactionSchema),
    defaultValues: {
      description: "",
      category_id: "",
      subcategory_id: "",
      type: "entrada",
      date: new Date(),
      amount: 0,
      comprovativo_url: ""
    }
  });

  // Calculate totals from current page data (this will be updated to use server-side totals later)
  const totalEntradas = transactions
    .filter((t) => t.type === "entrada")
    .reduce((sum, t) => sum + t.amount, 0);

  const totalSaidas = transactions
    .filter((t) => t.type === "saida")
    .reduce((sum, t) => sum + t.amount, 0);

  const saldo = totalEntradas - totalSaidas;

  // Reset to first page when filters change
  const handleFilterChange = () => {
    setCurrentPage(1);
  };

  const handleNewTransaction = () => {
    form.reset({
      description: "",
      category_id: "",
      subcategory_id: "",
      type: "entrada",
      date: new Date(),
      amount: 0,
      comprovativo_url: ""
    });
    form.clearErrors();
    setIsNewTransactionOpen(true);
  };

  const handleEditTransaction = (transaction: Transaction) => {
    setSelectedTransaction(transaction);
    form.reset({
      description: transaction.description,
      category_id: transaction.category_id || "",
      subcategory_id: transaction.subcategory_id || "",
      type: transaction.type,
      date: parseISO(transaction.date),
      amount: transaction.amount,
      comprovativo_url: transaction.comprovativo_url || ""
    });
    setIsEditTransactionOpen(true);
  };

  const handleDeleteTransaction = (transaction: Transaction) => {
    setSelectedTransaction(transaction);
    setIsDeleteTransactionOpen(true);
  };

  const handleRemoveFile = (transactionId: string) => {
    removeFile(transactionId);
    // Atualizar o formulário para remover a URL do arquivo
    form.setValue("comprovativo_url", "");
  };

  const handleViewComprovativo = (url: string) => {
    setCurrentComprovativoUrl(url);
    setIsViewComprovativoOpen(true);
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      // Apenas prepara o arquivo para upload, não faz upload imediato
      const tempUrl = prepareFileForUpload(file);
      form.setValue("comprovativo_url", tempUrl);
    } catch (error: any) {
      toastUtils.error(
        "Erro ao selecionar arquivo",
        `Erro: ${error.message}`
      );
    }
  };

  const onSubmitCreate = async (data: any) => {
    try {
      let comprovantivoUrl = data.comprovativo_url;

      // Se há um arquivo selecionado, fazer upload agora
      if (selectedFile && data.comprovativo_url?.startsWith('blob:')) {
        comprovantivoUrl = await uploadComprovativo();
      }

      const newTransaction = {
        description: data.description,
        category_id: data.category_id || null,
        subcategory_id: data.subcategory_id || null,
        type: data.type as "entrada" | "saida",
        date: format(data.date, "yyyy-MM-dd"),
        amount: Number(data.amount),
        comprovativo_url: comprovantivoUrl || null
      };

      createTransaction(newTransaction);
      setIsNewTransactionOpen(false);
      form.reset();
      clearSelectedFile();
    } catch (error: any) {
      toastUtils.error(
        "Erro ao salvar transação",
        `Erro: ${error.message}`
      );
    }
  };

  const onSubmitEdit = async (data: any) => {
    if (!selectedTransaction) return;

    try {
      let comprovantivoUrl = data.comprovativo_url;

      // Se há um arquivo selecionado, fazer upload agora
      if (selectedFile && data.comprovativo_url?.startsWith('blob:')) {
        console.log("Fazendo upload do arquivo antes de atualizar transação");
        comprovantivoUrl = await uploadComprovativo();
        console.log("Upload concluído, URL:", comprovantivoUrl);
      }

      // Validar dados antes de enviar
      const updatedTransaction = {
        id: selectedTransaction.id,
        description: data.description?.trim(),
        category_id: data.category_id || null,
        subcategory_id: data.subcategory_id || null,
        type: data.type as "entrada" | "saida",
        date: format(data.date, "yyyy-MM-dd"),
        amount: Number(data.amount),
        comprovativo_url: comprovantivoUrl || null
      };

      // Log detalhado dos dados antes de enviar
      console.log("Dados da transação editada:", JSON.stringify(updatedTransaction, null, 2));

      // Validações básicas
      if (!updatedTransaction.description || updatedTransaction.description.length < 2) {
        throw new Error("Descrição deve ter pelo menos 2 caracteres");
      }
      if (updatedTransaction.amount <= 0) {
        throw new Error("Valor deve ser maior que zero");
      }
      if (!updatedTransaction.type || !["entrada", "saida"].includes(updatedTransaction.type)) {
        throw new Error("Tipo deve ser 'entrada' ou 'saida'");
      }

      // Chamar updateTransaction
      // A mutação irá gerenciar os toasts de sucesso/erro automaticamente
      // O fechamento do modal será feito no callback onUpdateSuccess
      updateTransaction(updatedTransaction);

      // Atualizar o formulário com a nova URL do comprovativo
      if (comprovantivoUrl && comprovantivoUrl !== data.comprovativo_url) {
        form.setValue("comprovativo_url", comprovantivoUrl);
      }
    } catch (error: any) {
      // Este catch captura erros de upload e validação
      // Os erros de atualização da transação são tratados pela mutação
      console.error("Erro no onSubmitEdit:", error);
      toastUtils.error(
        "Erro ao processar transação",
        `Erro: ${error.message}`
      );
    }
  };

  const onConfirmDelete = () => {
    if (selectedTransaction) {
      deleteTransaction(selectedTransaction.id);
      setIsDeleteTransactionOpen(false);
    }
  };

  const columns: Column<Transaction>[] = [
    {
      header: "Descrição",
      accessorKey: "description",
      // Description column remains left-aligned (default)
    },
    {
      header: "Categoria",
      accessorKey: "categoria",
      className: "text-center",
    },
    {
      header: "Subcategoria",
      accessorKey: "subcategoria",
      className: "text-center",
    },
    {
      header: "Tipo",
      accessorKey: (row: Transaction) => (
        <span
          className={cn(
            "px-2 py-1 rounded text-xs font-medium",
            row.type === "entrada"
              ? "bg-green-100 text-green-800"
              : "bg-red-100 text-red-800"
          )}
        >
          {row.type === "entrada" ? "Entrada" : "Saída"}
        </span>
      ),
      className: "text-center",
    },
    {
      header: "Data",
      accessorKey: (row: Transaction) => format(new Date(row.date), "dd/MM/yyyy"),
      className: "text-center",
    },
    {
      header: "Valor",
      accessorKey: (row: Transaction) => (
        <span
          className={cn(
            row.type === "entrada" ? "text-green-600" : "text-red-600"
          )}
        >
          {(row.amount).toLocaleString("pt-AO", {
            style: "currency",
            currency: "AOA",
            minimumFractionDigits: 2,
          })}
        </span>
      ),
      className: "text-center",
    },
    {
      header: "Comprovativo",
      accessorKey: (row: Transaction) => (
        <div className="flex justify-center">
          {row.comprovativo_url ? (
            <Button
              variant="outline"
              size="icon"
              className="h-8 w-8"
              onClick={(e) => {
                e.stopPropagation();
                handleViewComprovativo(row.comprovativo_url!);
              }}
            >
              {row.comprovativo_url.toLowerCase().endsWith('.pdf') ? (
                <FileText className="h-4 w-4" />
              ) : (
                <Eye className="h-4 w-4" />
              )}
            </Button>
          ) : (
            <span className="text-muted-foreground text-xs">Nenhum</span>
          )}
        </div>
      ),
      className: "w-[100px] text-center",
    },
    {
      header: "Ações",
      accessorKey: (row: Transaction) => (
        <div className="flex justify-center space-x-2">
          <Button
            variant="outline"
            size="icon"
            className="h-8 w-8"
            onClick={(e) => {
              e.stopPropagation();
              handleEditTransaction(row);
            }}
          >
            <Pencil className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            className="h-8 w-8 text-red-600"
            onClick={(e) => {
              e.stopPropagation();
              handleDeleteTransaction(row);
            }}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      ),
      className: "w-[100px] text-center",
    },
  ];

  // Input de arquivo escondido
  const hiddenFileInput = (
    <input
      ref={fileInputRef}
      type="file"
      accept="image/png,image/jpeg,application/pdf"
      className="hidden"
      onChange={handleFileChange}
    />
  );

  return (
    <div className="animate-fade-in">
      <PageHeader
        title="Fluxo de Caixa"
        action={{
          label: "Nova Transação",
          icon: <Plus size={16} />,
          onClick: handleNewTransaction,
        }}
      />

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
        <StatCard
          title="Total de Entradas"
          value={(totalEntradas).toLocaleString("pt-AO", {
            style: "currency",
            currency: "AOA",
            minimumFractionDigits: 2,
          })}
          variant="green"
        />
        <StatCard
          title="Total de Saídas"
          value={(totalSaidas).toLocaleString("pt-AO", {
            style: "currency",
            currency: "AOA",
            minimumFractionDigits: 2,
          })}
          variant="red"
        />
        <StatCard
          title="Saldo"
          value={(saldo).toLocaleString("pt-AO", {
            style: "currency",
            currency: "AOA",
            minimumFractionDigits: 2,
          })}
          variant="blue"
        />
      </div>

      <div className="bg-white p-4 rounded-lg border shadow-sm mb-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <FilterIcon size={16} className="text-muted-foreground" />
            <h2 className="text-lg font-medium">Filtros Avançados</h2>
          </div>
          {(dateFrom || dateTo || tipo !== "todos" || categoria !== "todas" || subcategoria !== "todas" || searchQuery) && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setDateFrom(undefined);
                setDateTo(undefined);
                setTipo("todos");
                setCategoria("todas");
                setSubcategoria("todas");
                setSearchQuery("");
              }}
            >
              Limpar Filtros
            </Button>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Período</label>
            <div className="flex gap-2">
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full justify-start text-left font-normal date-button"
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {dateFrom ? (
                      format(dateFrom, "dd/MM/yyyy")
                    ) : (
                      <span className="text-muted-foreground">Data inicial</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0 pointer-events-auto calendar-popover-content" align="start">
                  <Calendar
                    mode="single"
                    selected={dateFrom}
                    onSelect={(date) => { setDateFrom(date); handleFilterChange(); }}
                    initialFocus
                    locale={pt}
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium invisible">Até</label>
            <div className="flex gap-2">
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full justify-start text-left font-normal date-button"
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {dateTo ? (
                      format(dateTo, "dd/MM/yyyy")
                    ) : (
                      <span className="text-muted-foreground">Data final</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0 pointer-events-auto calendar-popover-content" align="start">
                  <Calendar
                    mode="single"
                    selected={dateTo}
                    onSelect={(date) => { setDateTo(date); handleFilterChange(); }}
                    initialFocus
                    locale={pt}
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Categoria</label>
            <Select value={categoria} onValueChange={(value) => { setCategoria(value); handleFilterChange(); }}>
              <SelectTrigger>
                <SelectValue placeholder="Todas as categorias" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="todas">Todas as categorias</SelectItem>
                {categories?.map((cat) => (
                  <SelectItem key={cat.id} value={cat.id}>
                    {cat.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Subcategoria</label>
            <Select value={subcategoria} onValueChange={(value) => { setSubcategoria(value); handleFilterChange(); }}>
              <SelectTrigger>
                <SelectValue placeholder="Todas as subcategorias" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="todas">Todas as subcategorias</SelectItem>
                {subcategories?.filter(sub =>
                  !categoria || categoria === 'todas' || sub.category_id === categoria
                ).map((sub) => (
                  <SelectItem key={sub.id} value={sub.id}>
                    {sub.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Tipo</label>
            <Select value={tipo} onValueChange={(value) => { setTipo(value); handleFilterChange(); }}>
              <SelectTrigger>
                <SelectValue placeholder="Todos os tipos" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="todos">Todos os tipos</SelectItem>
                <SelectItem value="entrada">Entrada</SelectItem>
                <SelectItem value="saida">Saída</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      <div className="mb-4">
        <Input
          placeholder="Pesquisar transações..."
          value={searchQuery}
          onChange={(e) => { setSearchQuery(e.target.value); handleFilterChange(); }}
          className="max-w-sm"
        />
      </div>

      <DataTable
        columns={columns}
        data={transactions}
        emptyMessage="Nenhuma transação encontrada."
        loading={isLoading}
        pagination={paginationData}
        onPageChange={setCurrentPage}
        onPageSizeChange={(newPageSize) => {
          setPageSize(newPageSize);
          setCurrentPage(1);
        }}
      />

      {/* Modal de Nova Transação */}
      <Dialog
        open={isNewTransactionOpen}
        onOpenChange={(open) => {
          setIsNewTransactionOpen(open);
          if (!open) {
            clearSelectedFile();
          }
        }}
      >
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Nova Transação</DialogTitle>
            <DialogDescription>
              Adicione uma nova transação ao fluxo de caixa.
            </DialogDescription>
          </DialogHeader>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmitCreate)} className="space-y-4">
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Descrição<RequiredFieldMarker /></FormLabel>
                    <FormControl>
                      <Input placeholder="Descreva a transação" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="amount"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Valor<RequiredFieldMarker /></FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="0,00"
                          className="text-right"
                          {...field}
                          onChange={(e) => field.onChange(Number(e.target.value))}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="date"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Data<RequiredFieldMarker /></FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant="outline"
                              className="w-full justify-start text-left font-normal"
                            >
                              <CalendarIcon className="mr-2 h-4 w-4" />
                              {field.value ? (
                                format(field.value, "dd/MM/yyyy")
                              ) : (
                                <span className="text-muted-foreground">Selecione uma data</span>
                              )}
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0 pointer-events-auto calendar-popover-content" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) => date > new Date()}
                            initialFocus
                            locale={pt}
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tipo<RequiredFieldMarker /></FormLabel>
                    <Select
                      value={field.value}
                      onValueChange={field.onChange}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione o tipo" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="entrada">Entrada</SelectItem>
                        <SelectItem value="saida">Saída</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="category_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Categoria</FormLabel>
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {categories?.map((cat) => (
                            <SelectItem key={cat.id} value={cat.id}>
                              {cat.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="subcategory_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Subcategoria</FormLabel>
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {subcategories
                            ?.filter((sub) => !form.watch("category_id") || sub.category_id === form.watch("category_id"))
                            .map((sub) => (
                              <SelectItem key={sub.id} value={sub.id}>
                                {sub.name}
                              </SelectItem>
                            ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="comprovativo_url"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Comprovativo</FormLabel>
                    <div className="flex items-center gap-2">
                      <FormControl>
                        <Input
                          readOnly
                          value={field.value ? "Arquivo carregado" : "Nenhum arquivo selecionado"}
                          className="flex-grow"
                        />
                      </FormControl>
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => fileInputRef.current?.click()}
                        disabled={isUploading}
                      >
                        <FileUp className="h-4 w-4 mr-2" />
                        {isUploading ? "Carregando..." : "Upload"}
                      </Button>
                    </div>
                    {field.value && (
                      <div className="mt-2 text-sm text-blue-600">
                        <a href={field.value} target="_blank" rel="noopener noreferrer">
                          Visualizar arquivo
                        </a>
                      </div>
                    )}
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    setIsNewTransactionOpen(false);
                    form.reset();
                    clearSelectedFile();
                  }}
                >
                  Cancelar
                </Button>
                <Button type="submit" className="bg-twtwins-purple hover:bg-twtwins-dark-purple">
                  Salvar
                </Button>
              </DialogFooter>
            </form>
          </Form>
          {hiddenFileInput}
        </DialogContent>
      </Dialog>

      {/* Modal de Editar Transação */}
      <Dialog
        open={isEditTransactionOpen}
        onOpenChange={(open) => {
          setIsEditTransactionOpen(open);
          if (!open) {
            clearSelectedFile();
          }
        }}
      >
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Editar Transação</DialogTitle>
            <DialogDescription>
              Edite os detalhes da transação.
            </DialogDescription>
          </DialogHeader>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmitEdit)} className="space-y-4">
              {/* Mesmos campos do formulário de Nova Transação */}
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Descrição<RequiredFieldMarker /></FormLabel>
                    <FormControl>
                      <Input placeholder="Descreva a transação" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="amount"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Valor<RequiredFieldMarker /></FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="0,00"
                          className="text-right"
                          {...field}
                          onChange={(e) => field.onChange(Number(e.target.value))}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="date"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Data<RequiredFieldMarker /></FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant="outline"
                              className="w-full justify-start text-left font-normal"
                            >
                              <CalendarIcon className="mr-2 h-4 w-4" />
                              {field.value ? (
                                format(field.value, "dd/MM/yyyy")
                              ) : (
                                <span className="text-muted-foreground">Selecione uma data</span>
                              )}
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0 pointer-events-auto calendar-popover-content" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) => date > new Date()}
                            initialFocus
                            locale={pt}
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tipo<RequiredFieldMarker /></FormLabel>
                    <Select
                      value={field.value}
                      onValueChange={field.onChange}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione o tipo" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="entrada">Entrada</SelectItem>
                        <SelectItem value="saida">Saída</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="category_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Categoria</FormLabel>
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {categories?.map((cat) => (
                            <SelectItem key={cat.id} value={cat.id}>
                              {cat.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="subcategory_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Subcategoria</FormLabel>
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {subcategories
                            ?.filter((sub) => !form.watch("category_id") || sub.category_id === form.watch("category_id"))
                            .map((sub) => (
                              <SelectItem key={sub.id} value={sub.id}>
                                {sub.name}
                              </SelectItem>
                            ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="comprovativo_url"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Comprovativo</FormLabel>
                    <div className="flex items-center gap-2">
                      <FormControl>
                        <Input
                          readOnly
                          value={field.value ? "Arquivo carregado" : "Nenhum arquivo selecionado"}
                          className="flex-grow"
                        />
                      </FormControl>
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => fileInputRef.current?.click()}
                        disabled={isUploading}
                      >
                        <FileUp className="h-4 w-4 mr-2" />
                        {isUploading ? "Carregando..." : "Upload"}
                      </Button>
                      {field.value && (
                        <Button
                          type="button"
                          variant="outline"
                          size="icon"
                          onClick={() => selectedTransaction && handleRemoveFile(selectedTransaction.id)}
                          className="text-red-600 hover:text-red-700 hover:bg-red-50"
                          title="Remover arquivo"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                    {field.value && (
                      <div className="mt-2 text-sm text-blue-600">
                        <a href={field.value} target="_blank" rel="noopener noreferrer">
                          Visualizar arquivo
                        </a>
                      </div>
                    )}
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    setIsEditTransactionOpen(false);
                    clearSelectedFile();
                  }}
                >
                  Cancelar
                </Button>
                <Button type="submit" className="bg-twtwins-purple hover:bg-twtwins-dark-purple">
                  Salvar
                </Button>
              </DialogFooter>
            </form>
          </Form>
          {hiddenFileInput}
        </DialogContent>
      </Dialog>

      {/* Modal de Excluir Transação */}
      <Dialog open={isDeleteTransactionOpen} onOpenChange={setIsDeleteTransactionOpen}>
        <DialogContent className="sm:max-w-[400px]">
          <DialogHeader>
            <DialogTitle>Confirmar Exclusão</DialogTitle>
            <DialogDescription>
              Tem certeza que deseja excluir esta transação? Esta ação não pode ser desfeita.
            </DialogDescription>
          </DialogHeader>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsDeleteTransactionOpen(false)}
            >
              Cancelar
            </Button>
            <Button
              type="button"
              variant="destructive"
              onClick={onConfirmDelete}
            >
              Excluir
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Modal de Visualizar Comprovativo */}
      <Dialog open={isViewComprovativoOpen} onOpenChange={setIsViewComprovativoOpen}>
        <DialogContent className="sm:max-w-[800px]">
          <DialogHeader>
            <DialogTitle>Comprovativo</DialogTitle>
          </DialogHeader>

          <div className="flex justify-center p-4">
            {currentComprovativoUrl ? (
              currentComprovativoUrl.toLowerCase().endsWith('.pdf') ? (
                <iframe
                  src={currentComprovativoUrl}
                  className="w-full h-[500px]"
                  title="PDF Comprovativo"
                />
              ) : (
                <img
                  src={currentComprovativoUrl}
                  alt="Comprovativo"
                  className="max-w-full max-h-[500px] object-contain"
                />
              )
            ) : (
              <p className="text-muted-foreground">Nenhum comprovativo disponível.</p>
            )}
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsViewComprovativoOpen(false)}
            >
              Fechar
            </Button>
            {currentComprovativoUrl && (
              <Button
                type="button"
                onClick={() => window.open(currentComprovativoUrl, "_blank")}
              >
                Abrir em Nova Aba
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default FluxoDeCaixa;
