// Componente para diagnóstico de problemas de conectividade
// Ajuda a identificar e resolver problemas de rede

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { 
  Wifi, 
  WifiOff, 
  RefreshCw, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Info
} from 'lucide-react';
import { 
  runConnectivityDiagnostic, 
  getNetworkInfo, 
  testBackendConnectivity,
  setupConnectivityMonitoring 
} from '@/utils/networkUtils';

interface DiagnosticResult {
  networkInfo: ReturnType<typeof getNetworkInfo>;
  apiUrl: string;
  connectivityTest: Awaited<ReturnType<typeof testBackendConnectivity>>;
}

export const NetworkDiagnostic: React.FC = () => {
  const [isRunning, setIsRunning] = useState(false);
  const [result, setResult] = useState<DiagnosticResult | null>(null);
  const [isOnline, setIsOnline] = useState(navigator.onLine);

  useEffect(() => {
    // Configurar monitoramento de conectividade
    const cleanup = setupConnectivityMonitoring(setIsOnline);
    
    // Executar diagnóstico inicial
    runDiagnostic();
    
    return cleanup;
  }, []);

  const runDiagnostic = async () => {
    setIsRunning(true);
    try {
      const diagnosticResult = await runConnectivityDiagnostic();
      setResult(diagnosticResult);
    } catch (error) {
      console.error('Erro no diagnóstico:', error);
    } finally {
      setIsRunning(false);
    }
  };

  const getStatusIcon = (success: boolean) => {
    return success ? (
      <CheckCircle className="h-4 w-4 text-green-500" />
    ) : (
      <XCircle className="h-4 w-4 text-red-500" />
    );
  };

  const getStatusBadge = (success: boolean) => {
    return (
      <Badge variant={success ? "default" : "destructive"}>
        {success ? "Conectado" : "Falha"}
      </Badge>
    );
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {isOnline ? (
            <Wifi className="h-5 w-5 text-green-500" />
          ) : (
            <WifiOff className="h-5 w-5 text-red-500" />
          )}
          Diagnóstico de Conectividade
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Status da Internet */}
        <div className="flex items-center justify-between p-3 border rounded-lg">
          <div className="flex items-center gap-2">
            {getStatusIcon(isOnline)}
            <span className="font-medium">Status da Internet</span>
          </div>
          {getStatusBadge(isOnline)}
        </div>

        {/* Informações de Rede */}
        {result && (
          <div className="space-y-3">
            <h4 className="font-medium flex items-center gap-2">
              <Info className="h-4 w-4" />
              Informações de Rede
            </h4>
            
            <div className="grid grid-cols-2 gap-3 text-sm">
              <div>
                <span className="text-muted-foreground">Hostname:</span>
                <div className="font-mono">{result.networkInfo.hostname}</div>
              </div>
              <div>
                <span className="text-muted-foreground">Porta:</span>
                <div className="font-mono">{result.networkInfo.port || 'Padrão'}</div>
              </div>
              <div>
                <span className="text-muted-foreground">Protocolo:</span>
                <div className="font-mono">{result.networkInfo.protocol}</div>
              </div>
              <div>
                <span className="text-muted-foreground">Tipo:</span>
                <div>
                  {result.networkInfo.isLocalhost ? 'Localhost' : 
                   result.networkInfo.isLocalIP ? 'IP Local' : 'IP Externo'}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Teste de Conectividade com Backend */}
        {result && (
          <div className="space-y-3">
            <h4 className="font-medium">Conectividade com Backend</h4>
            
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center gap-2">
                {getStatusIcon(result.connectivityTest.success)}
                <div>
                  <div className="font-medium">Servidor Backend</div>
                  <div className="text-sm text-muted-foreground font-mono">
                    {result.connectivityTest.url}
                  </div>
                </div>
              </div>
              <div className="text-right">
                {getStatusBadge(result.connectivityTest.success)}
                {result.connectivityTest.responseTime && (
                  <div className="text-xs text-muted-foreground mt-1">
                    {result.connectivityTest.responseTime}ms
                  </div>
                )}
              </div>
            </div>

            {result.connectivityTest.error && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Erro:</strong> {result.connectivityTest.error}
                </AlertDescription>
              </Alert>
            )}
          </div>
        )}

        {/* Sugestões de Resolução */}
        {result && !result.connectivityTest.success && (
          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              <strong>Sugestões para resolver:</strong>
              <ul className="mt-2 space-y-1 text-sm">
                <li>• Verifique se o servidor backend está rodando na porta 3001</li>
                <li>• Confirme se o firewall não está bloqueando a conexão</li>
                <li>• Para acesso externo, use o IP correto no arquivo .env</li>
                <li>• Verifique as configurações de CORS no backend</li>
              </ul>
            </AlertDescription>
          </Alert>
        )}

        {/* Botão de Atualizar */}
        <Button 
          onClick={runDiagnostic} 
          disabled={isRunning}
          className="w-full"
        >
          {isRunning ? (
            <>
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              Executando Diagnóstico...
            </>
          ) : (
            <>
              <RefreshCw className="h-4 w-4 mr-2" />
              Executar Diagnóstico
            </>
          )}
        </Button>
      </CardContent>
    </Card>
  );
};
