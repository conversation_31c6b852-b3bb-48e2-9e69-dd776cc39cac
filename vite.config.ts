
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";

// https://vitejs.dev/config/
export default defineConfig(() => ({
  server: {
    host: process.env.VITE_HOST || "::",
    port: parseInt(process.env.VITE_PORT || "8083"),
  },
  plugins: [
    react(),
  ],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  optimizeDeps: {
    include: ['file-saver', 'papaparse', 'xlsx']
  },
  build: {
    commonjsOptions: {
      include: [/node_modules/]
    }
  }
}));
