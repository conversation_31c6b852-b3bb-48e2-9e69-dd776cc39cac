// MySQL API Configuration for javiagens

import { getApiBaseUrl } from '@/utils/networkUtils';

export const API_BASE_URL = getApiBaseUrl();

// API endpoints
export const API_ENDPOINTS = {
  // Authentication
  auth: {
    login: '/auth/login',
    logout: '/auth/logout',
    me: '/auth/me',
    refresh: '/auth/refresh',
    changePassword: '/auth/change-password',
  },
  
  // Users management
  users: {
    list: '/users',
    create: '/users',
    update: '/users',
    delete: '/users',
  },
  
  // Profiles
  profiles: {
    list: '/profiles',
    get: '/profiles',
    update: '/profiles',
  },
  
  // Categories
  categories: {
    list: '/categories',
    create: '/categories',
    update: '/categories',
    delete: '/categories',
  },
  
  // Subcategories
  subcategories: {
    list: '/subcategories',
    create: '/subcategories',
    update: '/subcategories',
    delete: '/subcategories',
  },
  
  // Transactions
  transactions: {
    list: '/transactions',
    create: '/transactions',
    update: '/transactions',
    delete: '/transactions',
  },
  
  // File uploads
  files: {
    upload: '/files/upload',
    delete: '/files',
    view: '/files/view',
  },
} as const;

// HTTP client configuration
export const httpClient = {
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
};

// Token management
export const tokenManager = {
  getToken: (): string | null => {
    return localStorage.getItem('auth_token');
  },
  
  setToken: (token: string): void => {
    localStorage.setItem('auth_token', token);
  },
  
  removeToken: (): void => {
    localStorage.removeItem('auth_token');
  },
  
  getAuthHeaders: (): Record<string, string> => {
    const token = tokenManager.getToken();
    return token ? { Authorization: `Bearer ${token}` } : {};
  },
};

// API response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T = any> extends ApiResponse<T[]> {
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Mapeamento de códigos de erro HTTP para mensagens em português
const getErrorMessage = (status: number, defaultMessage?: string): string => {
  const errorMessages: Record<number, string> = {
    400: 'Dados inválidos. Verifique as informações enviadas.',
    401: 'Sessão expirada. Por favor, faça login novamente.',
    403: 'Você não tem permissão para realizar esta ação.',
    404: 'Recurso não encontrado.',
    409: 'Conflito: este item já existe.',
    422: 'Dados inválidos. Verifique os campos obrigatórios.',
    429: 'Muitas tentativas. Tente novamente em alguns minutos.',
    500: 'Erro interno do servidor. Tente novamente mais tarde.',
    502: 'Serviço temporariamente indisponível.',
    503: 'Serviço em manutenção. Tente novamente mais tarde.',
  };

  return errorMessages[status] || defaultMessage || 'Erro inesperado. Tente novamente.';
};

// Tratamento de erros da API
export class ApiError extends Error {
  constructor(
    message: string,
    public status?: number,
    public code?: string
  ) {
    // Usar mensagem amigável em português se disponível
    const friendlyMessage = status ? getErrorMessage(status, message) : message;
    super(friendlyMessage);
    this.name = 'ApiError';
  }
}

// Métodos HTTP com tratamento de erro melhorado
export const httpMethods = {
  async get<T>(url: string, params?: Record<string, any>): Promise<ApiResponse<T>> {
    try {
      const queryString = params ? '?' + new URLSearchParams(params).toString() : '';
      const response = await fetch(`${API_BASE_URL}${url}${queryString}`, {
        method: 'GET',
        headers: {
          ...httpClient.headers,
          ...tokenManager.getAuthHeaders(),
        },
      });

      if (!response.ok) {
        // Tentar extrair mensagem de erro do servidor
        let errorMessage = '';
        try {
          const errorData = await response.json();
          errorMessage = errorData.error || errorData.message || '';
        } catch {
          // Se não conseguir parsear JSON, usar mensagem padrão
        }
        throw new ApiError(errorMessage, response.status);
      }

      return response.json();
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      // Erro de rede ou outro erro
      throw new ApiError('Erro de conexão. Verifique sua internet e tente novamente.');
    }
  },
  
  async post<T>(url: string, data?: any): Promise<ApiResponse<T>> {
    try {
      const response = await fetch(`${API_BASE_URL}${url}`, {
        method: 'POST',
        headers: {
          ...httpClient.headers,
          ...tokenManager.getAuthHeaders(),
        },
        body: data ? JSON.stringify(data) : undefined,
      });

      if (!response.ok) {
        let errorMessage = '';
        try {
          const errorData = await response.json();
          errorMessage = errorData.error || errorData.message || '';
        } catch {
          // Se não conseguir parsear JSON, usar mensagem padrão
        }
        throw new ApiError(errorMessage, response.status);
      }

      return response.json();
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError('Erro de conexão. Verifique sua internet e tente novamente.');
    }
  },
  
  async put<T>(url: string, data?: any): Promise<ApiResponse<T>> {
    try {
      const response = await fetch(`${API_BASE_URL}${url}`, {
        method: 'PUT',
        headers: {
          ...httpClient.headers,
          ...tokenManager.getAuthHeaders(),
        },
        body: data ? JSON.stringify(data) : undefined,
      });

      if (!response.ok) {
        let errorMessage = '';
        try {
          const errorData = await response.json();
          errorMessage = errorData.error || errorData.message || '';
        } catch {
          // Se não conseguir parsear JSON, usar mensagem padrão
        }
        throw new ApiError(errorMessage, response.status);
      }

      return response.json();
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError('Erro de conexão. Verifique sua internet e tente novamente.');
    }
  },

  async delete<T>(url: string): Promise<ApiResponse<T>> {
    try {
      const response = await fetch(`${API_BASE_URL}${url}`, {
        method: 'DELETE',
        headers: {
          ...httpClient.headers,
          ...tokenManager.getAuthHeaders(),
        },
      });

      if (!response.ok) {
        let errorMessage = '';
        try {
          const errorData = await response.json();
          errorMessage = errorData.error || errorData.message || '';
        } catch {
          // Se não conseguir parsear JSON, usar mensagem padrão
        }
        throw new ApiError(errorMessage, response.status);
      }

      return response.json();
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError('Erro de conexão. Verifique sua internet e tente novamente.');
    }
  },
};
