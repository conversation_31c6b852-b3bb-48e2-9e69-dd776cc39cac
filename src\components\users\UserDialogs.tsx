
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  Di<PERSON><PERSON>ooter,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Loader2 } from "lucide-react";
import { NewUserForm } from "./NewUserForm";
import { EditUserForm, User } from "./EditUserForm";
import { useState, useEffect } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { DatabaseAPI } from "@/api/database";
import { toastUtils } from "@/lib/toast-utils";
import { PermissionAlert } from "./PermissionAlert";

interface UserDialogsProps {
  isNewUserOpen: boolean;
  setIsNewUserOpen: (isOpen: boolean) => void;
  isEditDialogOpen: boolean;
  setIsEditDialogOpen: (isOpen: boolean) => void;
  isDeleteDialogOpen: boolean;
  setIsDeleteDialogOpen: (isOpen: boolean) => void;
  isStatusToggleDialogOpen: boolean;
  setIsStatusToggleDialogOpen: (isOpen: boolean) => void;
  selectedUser: User | null;
  editUserForm: Partial<User>;
  setEditUserForm: (user: Partial<User>) => void;
  userRole: string | null;
}

export const UserDialogs = ({
  isNewUserOpen,
  setIsNewUserOpen,
  isEditDialogOpen,
  setIsEditDialogOpen,
  isDeleteDialogOpen,
  setIsDeleteDialogOpen,
  isStatusToggleDialogOpen,
  setIsStatusToggleDialogOpen,
  selectedUser,
  editUserForm,
  setEditUserForm,
  userRole,
}: UserDialogsProps) => {
  const queryClient = useQueryClient();
  const isAdmin = userRole === 'administrador';

  // Mutação para deletar usuário permanentemente
  const deleteUserMutation = useMutation({
    mutationFn: async (userId: string) => {
      try {
        await DatabaseAPI.users.delete(userId);
      } catch (error: any) {
        throw new Error(error.message || 'Erro ao deletar usuário');
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      setIsDeleteDialogOpen(false);
      toast.success("Usuário deletado permanentemente!");
    },
    onError: (error) => {
      console.error(error);
      toast.error(`Erro ao deletar usuário: ${error.message}`);
    }
  });

  // Mutação para alternar status do usuário (ativar/desativar)
  const toggleUserStatusMutation = useMutation({
    mutationFn: async (user: User) => {
      try {
        const newStatus = !user.status;
        await DatabaseAPI.users.update(user.id, {
          status: newStatus,
        });
        return { user, newStatus };
      } catch (error: any) {
        throw new Error(error.message || 'Erro ao alterar status do usuário');
      }
    },
    onSuccess: ({ user, newStatus }) => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      setIsStatusToggleDialogOpen(false);
      toastUtils.success(`Usuário ${newStatus ? 'ativado' : 'desativado'} com sucesso!`);
    },
    onError: (error) => {
      console.error(error);
      toastUtils.error("Erro ao alterar status", error.message);
    }
  });

  // Mutação para desativar usuário
  const deactivateUserMutation = useMutation({
    mutationFn: async (userId: string) => {
      try {
        await DatabaseAPI.users.deactivate(userId);
      } catch (error: any) {
        throw new Error(error.message || 'Erro ao desativar usuário');
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      setIsDeleteDialogOpen(false);
      toast.success("Usuário desativado com sucesso!");
    },
    onError: (error) => {
      console.error(error);
      toast.error(`Erro ao desativar usuário: ${error.message}`);
    }
  });

  return (
    <>
      {/* Diálogo de novo usuário */}
      <Dialog open={isNewUserOpen} onOpenChange={setIsNewUserOpen}>
        <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Novo Usuário</DialogTitle>
            <DialogDescription>
              Adicione um novo usuário ao sistema.
            </DialogDescription>
          </DialogHeader>

          {isAdmin ? (
            <NewUserForm onClose={() => setIsNewUserOpen(false)} />
          ) : (
            <PermissionAlert action="criar" />
          )}

          {!isAdmin && (
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setIsNewUserOpen(false)}
              >
                Fechar
              </Button>
            </DialogFooter>
          )}
        </DialogContent>
      </Dialog>

      {/* Diálogo de edição de usuário */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Editar Usuário</DialogTitle>
            <DialogDescription>
              Altere as informações do usuário.
            </DialogDescription>
          </DialogHeader>

          {isAdmin ? (
            <EditUserForm
              user={editUserForm}
              onClose={() => setIsEditDialogOpen(false)}
            />
          ) : (
            <PermissionAlert action="editar" />
          )}

          {!isAdmin && (
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setIsEditDialogOpen(false)}
              >
                Fechar
              </Button>
            </DialogFooter>
          )}
        </DialogContent>
      </Dialog>

      {/* Diálogo de confirmação de status toggle */}
      <Dialog open={isStatusToggleDialogOpen} onOpenChange={setIsStatusToggleDialogOpen}>
        <DialogContent className="sm:max-w-[400px]">
          <DialogHeader>
            <DialogTitle>
              {selectedUser?.status ? 'Desativar Usuário' : 'Ativar Usuário'}
            </DialogTitle>
            <DialogDescription>
              {isAdmin ? (
                <>
                  Tem certeza que deseja {selectedUser?.status ? 'desativar' : 'ativar'} o usuário <strong>{selectedUser?.name}</strong>?
                  <div className="mt-3 p-3 border rounded-lg bg-blue-50 border-blue-200">
                    <div className="text-sm text-blue-600">
                      {selectedUser?.status
                        ? 'O usuário será desativado mas seus dados serão mantidos. Pode ser reativado posteriormente.'
                        : 'O usuário será reativado e poderá acessar o sistema novamente.'
                      }
                    </div>
                  </div>
                </>
              ) : (
                <PermissionAlert action="gerenciar" />
              )}
            </DialogDescription>
          </DialogHeader>

          <DialogFooter className="flex-col sm:flex-row gap-2">
            <Button
              variant="outline"
              onClick={() => setIsStatusToggleDialogOpen(false)}
              className="w-full sm:w-auto"
            >
              Cancelar
            </Button>
            {isAdmin && (
              <Button
                variant={selectedUser?.status ? "outline" : "default"}
                className={`w-full sm:w-auto ${
                  selectedUser?.status
                    ? 'bg-orange-50 border-orange-200 text-orange-700 hover:bg-orange-100'
                    : 'bg-green-50 border-green-200 text-green-700 hover:bg-green-100'
                }`}
                onClick={() => selectedUser && toggleUserStatusMutation.mutate(selectedUser)}
                disabled={toggleUserStatusMutation.isPending}
              >
                {toggleUserStatusMutation.isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {selectedUser?.status ? 'Desativando...' : 'Ativando...'}
                  </>
                ) : (
                  selectedUser?.status ? 'Desativar' : 'Ativar'
                )}
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Diálogo de confirmação de exclusão permanente */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[400px]">
          <DialogHeader>
            <DialogTitle>Deletar Usuário Permanentemente</DialogTitle>
            <DialogDescription>
              {isAdmin ? (
                <>
                  Tem certeza que deseja deletar permanentemente o usuário <strong>{selectedUser?.name}</strong>?
                  <div className="mt-3 p-3 border rounded-lg bg-red-50 border-red-200">
                    <div className="text-sm text-red-600">
                      Esta ação não pode ser desfeita. O usuário será removido permanentemente do sistema.
                    </div>
                  </div>
                </>
              ) : (
                <PermissionAlert action="gerenciar" />
              )}
            </DialogDescription>
          </DialogHeader>

          <DialogFooter className="flex-col sm:flex-row gap-2">
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
              className="w-full sm:w-auto"
            >
              Cancelar
            </Button>
            {isAdmin && (
              <Button
                variant="destructive"
                className="w-full sm:w-auto"
                onClick={() => selectedUser && deleteUserMutation.mutate(selectedUser.id)}
                disabled={deleteUserMutation.isPending}
              >
                {deleteUserMutation.isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Deletando...
                  </>
                ) : (
                  'Deletar Permanentemente'
                )}
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};
