
import { useMemo } from "react";
import { useQuery } from "@tanstack/react-query";
import { DatabaseAPI } from "@/api/database";
import { format, subMonths, startOfMonth, endOfMonth, isWithinInterval, parseISO } from "date-fns";
import { pt } from "date-fns/locale";
import { httpMethods, API_ENDPOINTS } from "@/api/config";

// Interface for transaction data
interface TransactionData {
  amount: number;
  date: string;
  type: "entrada" | "saida";
}

export const useDashboardData = () => {
  // Fetch transaction data from API
  const { data: transactions = [], isLoading } = useQuery({
    queryKey: ["dashboard-transactions"],
    queryFn: async () => {
      try {
        // Get transactions from the last 3 months to ensure we have enough data for comparison
        const threeMonthsAgo = subMonths(new Date(), 3);
        const formattedDate = format(threeMonthsAgo, 'yyyy-MM-dd');

        // Use the httpMethods to make the API call with proper base URL and auth headers
        const response = await httpMethods.get<TransactionData[]>(API_ENDPOINTS.transactions.list, {
          start_date: formattedDate,
          limit: 100,
          page: 1
        });

        if (!response.success) {
          throw new Error(response.error || 'Failed to fetch transactions');
        }

        const data = response.data || [];
        console.log("Dashboard: Fetched transactions count:", data.length);
        if (data.length > 0) {
          console.log("Dashboard: Transaction example:", JSON.stringify(data[0]));
        } else {
          console.log("Dashboard: No transactions found");
        }

        return data;
      } catch (error: any) {
        console.error("Dashboard: Error fetching transactions:", error);
        throw new Error(error.message || 'Failed to fetch transactions');
      }
    },
  });

  const dashboardData = useMemo(() => {
    if (!transactions.length) {
      console.log("Dashboard: No transactions to process for charts");
      return {
        annualData: [],
        compareData: [],
        summary: {
          totalEntradas: 0,
          totalSaidas: 0,
          saldoAtual: 0,
          percentChanges: {
            entradas: 0,
            saidas: 0,
            saldo: 0
          }
        }
      };
    }

    console.log("Dashboard: Processing", transactions.length, "transactions for charts");

    // Get current date and date ranges for current and previous month
    const currentDate = new Date();
    const currentMonthStart = startOfMonth(currentDate);
    const currentMonthEnd = endOfMonth(currentDate);
    const previousMonthStart = startOfMonth(subMonths(currentDate, 1));
    const previousMonthEnd = endOfMonth(subMonths(currentDate, 1));

    // Group transactions by month
    const monthlyData = transactions.reduce((acc, transaction) => {
      const date = new Date(transaction.date);
      const month = format(date, "MMM", { locale: pt }); // Get short month name in Portuguese
      
      if (!acc[month]) {
        acc[month] = {
          entradas: 0,
          saidas: 0
        };
      }
      
      if (transaction.type === "entrada") {
        acc[month].entradas += Number(transaction.amount);
      } else {
        acc[month].saidas += Number(transaction.amount);
      }
      
      return acc;
    }, {} as Record<string, { entradas: number; saidas: number }>);

    // Generate annual data for the line chart
    const annualData = Object.keys(monthlyData).map(month => ({
      name: month,
      valor: monthlyData[month].entradas - monthlyData[month].saidas
    }));

    // Generate comparison data for the bar chart (last 6 months)
    const compareData = Object.keys(monthlyData)
      .slice(-6)
      .map(month => ({
        name: month,
        entradas: monthlyData[month].entradas,
        saidas: monthlyData[month].saidas
      }));

    // Filter transactions for current month and previous month
    const currentMonthTransactions = transactions.filter(t => {
      const transactionDate = parseISO(t.date);
      return isWithinInterval(transactionDate, {
        start: currentMonthStart,
        end: currentMonthEnd
      });
    });
    
    const previousMonthTransactions = transactions.filter(t => {
      const transactionDate = parseISO(t.date);
      return isWithinInterval(transactionDate, {
        start: previousMonthStart,
        end: previousMonthEnd
      });
    });

    // Calculate total entradas and saidas for current and previous month
    const currentEntradas = currentMonthTransactions.reduce(
      (sum, t) => (t.type === "entrada" ? sum + Number(t.amount) : sum),
      0
    );

    const currentSaidas = currentMonthTransactions.reduce(
      (sum, t) => (t.type === "saida" ? sum + Number(t.amount) : sum),
      0
    );

    const previousEntradas = previousMonthTransactions.reduce(
      (sum, t) => (t.type === "entrada" ? sum + Number(t.amount) : sum),
      0
    );

    const previousSaidas = previousMonthTransactions.reduce(
      (sum, t) => (t.type === "saida" ? sum + Number(t.amount) : sum),
      0
    );

    const currentSaldo = currentEntradas - currentSaidas;
    const previousSaldo = previousEntradas - previousSaidas;

    // Calculate month-over-month percentage changes
    // Handle edge cases like division by zero or no previous month data
    /**
     * Calculates percentage change between two values
     * @param current Current month value
     * @param previous Previous month value
     * @returns Percentage change (uncapped, to be capped later in UI)
     */
    const calculatePercentChange = (current: number, previous: number): number => {
      // If both values are zero, there's no change
      if (current === 0 && previous === 0) return 0;
      
      // If previous value is zero, show 100% increase if current is positive
      if (previous === 0) return current > 0 ? 100 : -100;
      
      const change = ((current - previous) / Math.abs(previous)) * 100;
      // Round to one decimal place
      return Math.round(change * 10) / 10;
    };

    const percentChanges = {
      entradas: calculatePercentChange(currentEntradas, previousEntradas),
      saidas: calculatePercentChange(currentSaidas, previousSaidas),
      saldo: calculatePercentChange(currentSaldo, previousSaldo)
    };

    console.log("Dashboard: Current month entradas:", currentEntradas);
    console.log("Dashboard: Previous month entradas:", previousEntradas);
    console.log("Dashboard: Entradas % change (uncapped):", percentChanges.entradas);
    
    console.log("Dashboard: Current month saídas:", currentSaidas);
    console.log("Dashboard: Previous month saídas:", previousSaidas);
    console.log("Dashboard: Saídas % change (uncapped):", percentChanges.saidas);
    
    console.log("Dashboard: Current month saldo:", currentSaldo);
    console.log("Dashboard: Previous month saldo:", previousSaldo);
    console.log("Dashboard: Saldo % change (uncapped):", percentChanges.saldo);

    // Calculate summary statistics
    const totalEntradas = transactions.reduce((sum, t) =>
      t.type === "entrada" ? sum + Number(t.amount) : sum, 0);

    const totalSaidas = transactions.reduce((sum, t) =>
      t.type === "saida" ? sum + Number(t.amount) : sum, 0);
    
    const saldoAtual = totalEntradas - totalSaidas;

    return {
      annualData,
      compareData,
      summary: {
        totalEntradas,
        totalSaidas,
        saldoAtual,
        percentChanges
      }
    };
  }, [transactions]);
  
  return { 
    ...dashboardData, 
    isLoading 
  };
};
