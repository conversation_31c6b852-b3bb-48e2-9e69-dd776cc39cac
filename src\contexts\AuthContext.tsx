import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { AuthAPI } from '@/api/auth';
import { toastUtils } from '@/lib/toast-utils';

interface User {
  id: string;
  email: string;
  name: string;
  role: 'administrador' | 'gerente';
}

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const isAuthenticated = !!user;

  const refreshUser = async () => {
    try {
      const session = await AuthAPI.getSession();

      if (!session.user) {
        setUser(null);
        return;
      }

      setUser({
        id: session.user.id,
        email: session.user.email,
        name: session.user.name,
        role: session.user.role,
      });
    } catch (error) {
      console.error('Error refreshing user:', error);
      setUser(null);
    }
  };

  const login = async (email: string, password: string) => {
    setIsLoading(true);
    try {
      const response = await AuthAPI.login({ email, password });

      setUser({
        id: response.user.id,
        email: response.user.email,
        name: response.user.name,
        role: response.user.role,
      });

      toastUtils.auth.loginSuccess();
    } catch (error: any) {
      console.error("Erro ao fazer login:", error);
      const errorMessage = error.message || "Erro ao fazer login. Verifique suas credenciais.";
      toastUtils.auth.loginError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      await AuthAPI.logout();
      setUser(null);
      toastUtils.auth.logoutSuccess();
    } catch (error) {
      console.error('Erro ao fazer logout:', error);
      toastUtils.auth.loginError('Erro ao fazer logout');
    }
  };

  useEffect(() => {
    // Verificar sessão inicial
    const initializeAuth = async () => {
      setIsLoading(true);
      await refreshUser();
      setIsLoading(false);
    };

    initializeAuth();

    // Escutar mudanças na autenticação
    const authCleanup = AuthAPI.onAuthStateChange((user) => {
      console.log('Auth state changed:', user);

      if (!user) {
        setUser(null);
        setIsLoading(false);
      } else {
        setUser({
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role,
        });
        setIsLoading(false);
      }
    });

    return () => {
      if (typeof authCleanup === 'function') {
        authCleanup();
      }
    };
  }, []);

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated,
    login,
    logout,
    refreshUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
