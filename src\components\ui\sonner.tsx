/**
 * Sonner Toast Component
 *
 * This component provides a standardized toast notification system using the Sonner library.
 * It includes:
 * - Colored backgrounds for different toast types (success: green, error: red, warning: orange, info: blue)
 * - White progress bars that are visible on colored backgrounds
 * - Close button positioned on the left side
 * - Consistent styling across the application
 *
 * Progress Bar Implementation:
 * - Uses Sonner's built-in progress bar functionality
 * - Custom CSS in index.css ensures white progress bars are visible on colored backgrounds
 * - Progress bars automatically animate based on toast duration (4 seconds)
 *
 * Styling Choices:
 * - Close button positioned on the right side for standard UX
 * - White text on colored backgrounds for optimal contrast and readability
 * - No borders for cleaner appearance
 * - Semi-transparent progress bars (80-90% opacity) for subtle animation
 */
import { useTheme } from "next-themes"
import { Toaster as Sonner, toast } from "sonner"

type ToasterProps = React.ComponentProps<typeof Sonner>

/**
 * Main Toaster component that renders the toast notifications
 * Configured with standardized settings for the application
 */
const Toaster = ({ ...props }: ToasterProps) => {
  const { theme = "system" } = useTheme()

  return (
    <Sonner
      theme={theme as ToasterProps["theme"]}
      className="toaster group"
      position="bottom-right"
      duration={4000}
      visibleToasts={5}
      closeButton
      toastOptions={{
        classNames: {
          toast:
            "group toast group-[.toaster]:bg-background group-[.toaster]:text-white group-[.toaster]:border-0 group-[.toaster]:shadow-lg",
          description: "group-[.toast]:text-white group-[.toast]:opacity-90",
          actionButton:
            "group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",
          cancelButton:
            "group-[.toast]:bg-muted group-[.toast]:text-muted-foreground",
          closeButton:
            "group-[.toast]:absolute group-[.toast]:right-2 group-[.toast]:top-2 group-[.toast]:left-auto group-[.toast]:text-white group-[.toast]:hover:text-gray-200",
          // Progress bar styling - make it visible with white color on colored backgrounds
          loader: "group-[.toast]:bg-white group-[.toast]:opacity-80",
        },
        style: {
          // Ensure progress bar is visible
          '--normal-bg': 'rgba(255, 255, 255, 0.2)',
          '--normal-border': 'rgba(255, 255, 255, 0.3)',
          '--normal-text': 'white',
          '--success-bg': '#22c55e',
          '--success-border': '#16a34a',
          '--success-text': 'white',
          '--error-bg': '#ef4444',
          '--error-border': '#dc2626',
          '--error-text': 'white',
          '--warning-bg': '#f97316',
          '--warning-border': '#ea580c',
          '--warning-text': 'white',
          '--info-bg': '#3b82f6',
          '--info-border': '#2563eb',
          '--info-text': 'white',
        } as React.CSSProperties,
      }}
      {...props}
    />
  )
}

export { Toaster, toast }
