
import { useState } from "react";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DialogFooter } from "@/components/ui/dialog";
import { Loader2, Eye, EyeOff } from "lucide-react";
import { DatabaseAPI } from "@/api/database";
import { toastUtils } from "@/lib/toast-utils";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { PasswordStrengthIndicator, calculatePasswordStrength } from "@/components/ui/password-strength-indicator";

// Função para formatar número de telefone angolano
const formatAngolanPhone = (value: string): string => {
  // Remove todos os caracteres não numéricos
  const numbers = value.replace(/\D/g, '');

  // Se começar com 244, remove para evitar duplicação
  const cleanNumbers = numbers.startsWith('244') ? numbers.slice(3) : numbers;

  // Limita a 9 dígitos
  const limitedNumbers = cleanNumbers.slice(0, 9);

  // Aplica a formatação (+244) xxx xxx xxx
  if (limitedNumbers.length === 0) return '';
  if (limitedNumbers.length <= 3) return `(+244) ${limitedNumbers}`;
  if (limitedNumbers.length <= 6) return `(+244) ${limitedNumbers.slice(0, 3)} ${limitedNumbers.slice(3)}`;
  return `(+244) ${limitedNumbers.slice(0, 3)} ${limitedNumbers.slice(3, 6)} ${limitedNumbers.slice(6)}`;
};

export interface NewUserFormData {
  name: string;
  email: string;
  phone: string;
  role: "administrador" | "gerente";
  password: string;
  confirmPassword: string;
}

const initialNewUserForm: NewUserFormData = {
  name: "",
  email: "",
  phone: "",
  role: "gerente",
  password: "",
  confirmPassword: "",
};

interface NewUserFormProps {
  onClose: () => void;
}

export const NewUserForm = ({ onClose }: NewUserFormProps) => {
  const [newUserForm, setNewUserForm] = useState<NewUserFormData>(initialNewUserForm);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const queryClient = useQueryClient();
  
  // Mutação para criar novo usuário
  const createUserMutation = useMutation({
    mutationFn: async (userData: NewUserFormData) => {
      try {
        const result = await DatabaseAPI.users.create({
          name: userData.name,
          email: userData.email,
          password: userData.password,
          phone: userData.phone,
          role: userData.role,
        });

        return result;
      } catch (error: any) {
        console.error("Erro na criação do usuário:", error);
        throw new Error(error.message || 'Erro ao criar usuário');
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      onClose();
      toastUtils.user.created();
    },
    onError: (error) => {
      console.error(error);
      toastUtils.user.error("criar", error.message);
    }
  });
  
  const handleNewUserSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (newUserForm.password !== newUserForm.confirmPassword) {
      toastUtils.error("Erro de validação", "As senhas não coincidem!");
      return;
    }

    // Check password strength
    const passwordStrength = calculatePasswordStrength(newUserForm.password);
    if (passwordStrength.score < 2) {
      toastUtils.error("Senha muito fraca", "Por favor, use uma senha mais forte.");
      return;
    }

    createUserMutation.mutate(newUserForm);
  };
  
  return (
    <form onSubmit={handleNewUserSubmit}>
      <div className="grid gap-4 py-4">
        <div className="space-y-2">
          <label className="text-sm font-medium">
            Nome <span className="text-red-500">*</span>
          </label>
          <Input
            placeholder="Nome completo do usuário"
            value={newUserForm.name}
            onChange={(e) => setNewUserForm({...newUserForm, name: e.target.value})}
            required
          />
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium">
            Email <span className="text-red-500">*</span>
          </label>
          <Input
            type="email"
            placeholder="<EMAIL>"
            value={newUserForm.email}
            onChange={(e) => setNewUserForm({...newUserForm, email: e.target.value})}
            required
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Telefone</label>
            <Input
              placeholder="(+244) xxx xxx xxx"
              value={newUserForm.phone}
              onChange={(e) => {
                const formatted = formatAngolanPhone(e.target.value);
                setNewUserForm({...newUserForm, phone: formatted});
              }}
            />
          </div>
          
          <div className="space-y-2">
            <label className="text-sm font-medium">
              Perfil <span className="text-red-500">*</span>
            </label>
            <Select
              value={newUserForm.role}
              onValueChange={(value: "administrador" | "gerente") => setNewUserForm({...newUserForm, role: value})}
              required
            >
              <SelectTrigger>
                <SelectValue placeholder="Selecione" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="administrador">Administrador</SelectItem>
                <SelectItem value="gerente">Gerente</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium">
            Senha <span className="text-red-500">*</span>
          </label>
          <div className="relative">
            <Input
              type={showPassword ? "text" : "password"}
              placeholder="Nova senha"
              value={newUserForm.password}
              onChange={(e) => setNewUserForm({...newUserForm, password: e.target.value})}
              className="pr-10"
              required
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
            >
              {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
            </button>
          </div>
          {/* Password Strength Indicator */}
          {newUserForm.password && (
            <PasswordStrengthIndicator
              password={newUserForm.password}
              showFeedback={true}
              language="pt"
            />
          )}
        </div>
        
        <div className="space-y-2">
          <label className="text-sm font-medium">
            Confirmar Senha <span className="text-red-500">*</span>
          </label>
          <div className="relative">
            <Input
              type={showConfirmPassword ? "text" : "password"}
              placeholder="Confirme a senha"
              value={newUserForm.confirmPassword}
              onChange={(e) => setNewUserForm({...newUserForm, confirmPassword: e.target.value})}
              className="pr-10"
              required
            />
            <button
              type="button"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
            >
              {showConfirmPassword ? <EyeOff size={16} /> : <Eye size={16} />}
            </button>
          </div>
        </div>
      </div>
      
      <DialogFooter>
        <Button 
          variant="outline" 
          onClick={onClose} 
          type="button"
        >
          Cancelar
        </Button>
        <Button 
          className="bg-twtwins-purple hover:bg-twtwins-dark-purple"
          type="submit"
          disabled={createUserMutation.isPending}
        >
          {createUserMutation.isPending ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Salvando...
            </>
          ) : (
            'Salvar'
          )}
        </Button>
      </DialogFooter>
    </form>
  );
};
