
import { useState } from "react";
import { PageHeader } from "@/components/PageHeader";
import { DataTable } from "@/components/DataTable";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Loader2, Plus, Search } from "lucide-react";
import { UserDialogs } from "@/components/users/UserDialogs";
import { getUserColumns } from "@/components/users/UserColumns";
import { useUsers } from "@/hooks/useUsers";
import { User } from "@/components/users/EditUserForm";
import { useAuth } from "@/contexts/AuthContext";

const Usuarios = () => {
  const [isNewUserOpen, setIsNewUserOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isStatusToggleDialogOpen, setIsStatusToggleDialogOpen] = useState(false);
  const [isEditDialogO<PERSON>, setIsEditDialogOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [editUserForm, setEditUserForm] = useState<Partial<User>>({});

  const { user } = useAuth();
  const { filteredUsers, isLoading, error } = useUsers(searchQuery);

  // Get user role from auth context
  const currentUserRole = user?.role || null;

  const handleNewUser = () => {
    setIsNewUserOpen(true);
  };

  const handleEditUser = (user: User) => {
    setEditUserForm({
      id: user.id,
      name: user.name,
      email: user.email,
      phone: user.phone,
      role: user.role,
      status: user.status
    });
    setIsEditDialogOpen(true);
  };

  const handleToggleUserStatus = (user: User) => {
    setSelectedUser(user);
    setIsStatusToggleDialogOpen(true);
  };

  const handleDeleteUser = (user: User) => {
    setSelectedUser(user);
    setIsDeleteDialogOpen(true);
  };

  const columns = getUserColumns({
    onEdit: handleEditUser,
    onToggleStatus: handleToggleUserStatus,
    onDelete: handleDeleteUser,
    userRole: currentUserRole
  });

  const isAdmin = currentUserRole === 'administrador';

  return (
    <div className="animate-fade-in">
      <PageHeader
        title="Usuários"
        action={isAdmin ? {
          label: "Novo Usuário",
          icon: <Plus size={16} />,
          onClick: handleNewUser,
        } : undefined}
      />

      <div className="mb-6">
        <div className="flex items-center gap-3">
          <div className="relative max-w-sm">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Pesquisar usuários..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-9"
            />
          </div>
          {searchQuery && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setSearchQuery("")}
            >
              Limpar Filtros
            </Button>
          )}
        </div>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-twtwins-purple" />
        </div>
      ) : error ? (
        <div className="text-center text-red-500 p-4">
          Erro ao carregar usuários. Por favor, tente novamente.
        </div>
      ) : (
        <DataTable
          columns={columns}
          data={filteredUsers}
          emptyMessage="Nenhum usuário encontrado."
        />
      )}

      <UserDialogs
        isNewUserOpen={isNewUserOpen}
        setIsNewUserOpen={setIsNewUserOpen}
        isEditDialogOpen={isEditDialogOpen}
        setIsEditDialogOpen={setIsEditDialogOpen}
        isDeleteDialogOpen={isDeleteDialogOpen}
        setIsDeleteDialogOpen={setIsDeleteDialogOpen}
        isStatusToggleDialogOpen={isStatusToggleDialogOpen}
        setIsStatusToggleDialogOpen={setIsStatusToggleDialogOpen}
        selectedUser={selectedUser}
        editUserForm={editUserForm}
        setEditUserForm={setEditUserForm}
        userRole={currentUserRole}
      />
    </div>
  );
};

export default Usuarios;
