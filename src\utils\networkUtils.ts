// Utilitários para diagnóstico e resolução de problemas de rede
// Ajuda a identificar problemas de conectividade entre frontend e backend

/**
 * Detecta se a aplicação está sendo acessada via IP local ou externo
 */
export const getNetworkInfo = () => {
  const hostname = window.location.hostname;
  const port = window.location.port;
  const protocol = window.location.protocol;
  
  const isLocalhost = hostname === 'localhost' || hostname === '127.0.0.1';
  const isLocalIP = hostname.startsWith('192.168.') || hostname.startsWith('10.') || hostname.startsWith('172.');
  
  return {
    hostname,
    port,
    protocol,
    isLocalhost,
    isLocalIP,
    fullUrl: `${protocol}//${hostname}${port ? `:${port}` : ''}`
  };
};

/**
 * Gera URL da API baseada no contexto de rede atual
 */
export const getApiBaseUrl = () => {
  const networkInfo = getNetworkInfo();
  const envApiUrl = import.meta.env.VITE_API_BASE_URL;
  
  // Se há uma URL específica no .env, usar ela
  if (envApiUrl && !envApiUrl.includes('localhost')) {
    return envApiUrl;
  }
  
  // Se estamos acessando via IP externo, ajustar a URL da API
  if (networkInfo.isLocalIP) {
    return `${networkInfo.protocol}//${networkInfo.hostname}:3001/api`;
  }
  
  // Fallback para localhost
  return envApiUrl || 'http://localhost:3001/api';
};

/**
 * Testa conectividade com o backend
 */
export const testBackendConnectivity = async (apiUrl?: string): Promise<{
  success: boolean;
  url: string;
  error?: string;
  responseTime?: number;
}> => {
  const testUrl = apiUrl || getApiBaseUrl();
  const healthUrl = testUrl.replace('/api', '') + '/health';
  
  const startTime = Date.now();
  
  try {
    const response = await fetch(healthUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      // Timeout de 5 segundos
      signal: AbortSignal.timeout(5000)
    });
    
    const responseTime = Date.now() - startTime;
    
    if (response.ok) {
      const data = await response.json();
      return {
        success: true,
        url: healthUrl,
        responseTime
      };
    } else {
      return {
        success: false,
        url: healthUrl,
        error: `HTTP ${response.status}: ${response.statusText}`,
        responseTime
      };
    }
  } catch (error: any) {
    const responseTime = Date.now() - startTime;
    
    let errorMessage = 'Erro de conectividade';
    
    if (error.name === 'AbortError') {
      errorMessage = 'Timeout - Servidor não respondeu em 5 segundos';
    } else if (error.message.includes('Failed to fetch')) {
      errorMessage = 'Não foi possível conectar ao servidor - Verifique se o backend está rodando';
    } else {
      errorMessage = error.message;
    }
    
    return {
      success: false,
      url: healthUrl,
      error: errorMessage,
      responseTime
    };
  }
};

/**
 * Diagnóstico completo de conectividade
 */
export const runConnectivityDiagnostic = async () => {
  const networkInfo = getNetworkInfo();
  const apiUrl = getApiBaseUrl();
  
  console.group('🔍 Diagnóstico de Conectividade');
  console.log('📍 Informações de Rede:', networkInfo);
  console.log('🔗 URL da API:', apiUrl);
  
  // Testar conectividade
  const connectivityTest = await testBackendConnectivity(apiUrl);
  console.log('🌐 Teste de Conectividade:', connectivityTest);
  
  // Testar URLs alternativas se o teste principal falhar
  if (!connectivityTest.success) {
    console.log('❌ Teste principal falhou, tentando URLs alternativas...');
    
    const alternativeUrls = [
      'http://localhost:3001',
      'http://127.0.0.1:3001',
      `http://${networkInfo.hostname}:3001`
    ];
    
    for (const url of alternativeUrls) {
      if (url !== apiUrl.replace('/api', '')) {
        const altTest = await testBackendConnectivity(url + '/api');
        console.log(`🔄 Teste alternativo (${url}):`, altTest);
        
        if (altTest.success) {
          console.log(`✅ URL alternativa funcionando: ${url}/api`);
          break;
        }
      }
    }
  }
  
  console.groupEnd();
  
  return {
    networkInfo,
    apiUrl,
    connectivityTest
  };
};

/**
 * Monitora mudanças de conectividade
 */
export const setupConnectivityMonitoring = (onConnectivityChange?: (isOnline: boolean) => void) => {
  const handleOnline = () => {
    console.log('🟢 Conectividade restaurada');
    onConnectivityChange?.(true);
  };
  
  const handleOffline = () => {
    console.log('🔴 Conectividade perdida');
    onConnectivityChange?.(false);
  };
  
  window.addEventListener('online', handleOnline);
  window.addEventListener('offline', handleOffline);
  
  // Cleanup function
  return () => {
    window.removeEventListener('online', handleOnline);
    window.removeEventListener('offline', handleOffline);
  };
};
