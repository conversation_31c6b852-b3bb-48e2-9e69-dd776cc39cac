
import { But<PERSON> } from "@/components/ui/button";
import { DataTable, Column } from "@/components/DataTable";
import { Pencil, Trash2, Plus } from "lucide-react";

export interface Category {
  id: string;
  name: string;
}

interface CategoryTableProps {
  categories: Category[];
  isLoading: boolean;
  handleNewCategory: () => void;
  handleEditCategory: (category: Category) => void;
  handleDeleteCategory: (category: Category) => void;
}

export function CategoryTable({ 
  categories, 
  isLoading, 
  handleNewCategory, 
  handleEditCategory, 
  handleDeleteCategory 
}: CategoryTableProps) {
  
  const categoryColumns: Column<Category>[] = [
    {
      header: "Nome",
      accessorKey: "name",
    },
    {
      header: "Ações",
      accessorKey: (row: Category) => (
        <div className="flex justify-center space-x-2">
          <Button variant="outline" size="icon" className="h-8 w-8" onClick={() => handleEditCategory(row)}>
            <Pencil className="h-4 w-4" />
          </Button>
          <Button variant="outline" size="icon" className="h-8 w-8 text-red-600" onClick={() => handleDeleteCategory(row)}>
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      ),
      className: "w-[100px] text-center",
    },
  ];

  return (
    <div className="bg-white p-6 rounded-lg border shadow-sm">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-lg font-medium">Gerenciar Categorias</h2>
        <Button 
          className="bg-twtwins-purple hover:bg-twtwins-dark-purple"
          onClick={handleNewCategory}
        >
          <Plus size={16} className="mr-2" />
          Nova Categoria
        </Button>
      </div>
      
      <DataTable
        columns={categoryColumns}
        data={categories}
        emptyMessage="Nenhuma categoria encontrada."
        loading={isLoading}
      />
    </div>
  );
}
