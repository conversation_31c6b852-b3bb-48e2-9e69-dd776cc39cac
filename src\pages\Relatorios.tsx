
import { PageHeader } from "@/components/PageHeader";
import { useTransactions } from "@/hooks/useTransactions";
import StatisticsSection from "@/features/relatorios/StatisticsSection";
import ReportsSection from "@/features/relatorios/ReportsSection";
import TransactionsSection from "@/features/relatorios/TransactionsSection";

const Relatorios = () => {
  // Hook para estatísticas (usa paginação padrão)
  const { transactions, pagination, isLoading } = useTransactions();

  // Hook para buscar TODAS as transações para a tabela de relatórios (usando limite maior)
  const { transactions: allTransactions, isLoading: allTransactionsLoading } = useTransactions({}, { limit: 100 });

  return (
    <div className="animate-fade-in">
      <PageHeader title="Relatórios" />

      {/* Summary statistics */}
      <StatisticsSection transactions={transactions} pagination={pagination} />

      {/* Available reports */}
      <ReportsSection transactions={allTransactions} isLoading={allTransactionsLoading} />

      {/* Transactions table - usando todas as transações */}
      {allTransactions && !allTransactionsLoading && (
        <TransactionsSection transactions={allTransactions} isLoading={allTransactionsLoading} />
      )}
    </div>
  );
};

export default Relatorios;
