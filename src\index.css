
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 354 82% 52%; /* javiagens Red */
    --primary-foreground: 0 0% 100%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 354 82% 52%; /* javiagens Red */
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 354 82% 52%; /* javiagens Red */
    --sidebar-foreground: 0 0% 100%;
    --sidebar-primary: 0 0% 100%;
    --sidebar-primary-foreground: 354 82% 52%;
    --sidebar-accent: 354 70% 65%;
    --sidebar-accent-foreground: 0 0% 100%;
    --sidebar-border: 354 70% 45%;
    --sidebar-ring: 354 82% 52%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  .stat-card {
    @apply p-6 rounded-lg border bg-card text-card-foreground shadow-sm;
  }
  .stat-card-green {
    @apply border-l-8 border-l-javiagens-green;
  }
  .stat-card-red {
    @apply border-l-8 border-l-javiagens-red;
  }
  .stat-card-blue {
    @apply border-l-8 border-l-javiagens-blue;
  }
  .stat-card-purple {
    @apply border-l-8 border-l-javiagens-red;
  }
  .percent-positive {
    @apply text-javiagens-green flex items-center gap-1;
  }
  .percent-negative {
    @apply text-javiagens-red flex items-center gap-1;
  }

  /* Toast Progress Bar Styling - Custom Implementation */
  [data-sonner-toaster] [data-sonner-toast]::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 0 0 6px 6px;
    width: 100%;
    animation: toast-progress 4s linear forwards;
    z-index: 1;
  }

  /* Progress bar animation */
  @keyframes toast-progress {
    from {
      width: 100%;
    }
    to {
      width: 0%;
    }
  }

  /* Ensure progress bar is visible on colored backgrounds */
  [data-sonner-toaster] [data-sonner-toast][data-type="success"]::after {
    background-color: rgba(255, 255, 255, 0.9);
  }

  [data-sonner-toaster] [data-sonner-toast][data-type="error"]::after {
    background-color: rgba(255, 255, 255, 0.9);
  }

  [data-sonner-toaster] [data-sonner-toast][data-type="warning"]::after {
    background-color: rgba(255, 255, 255, 0.9);
  }

  [data-sonner-toaster] [data-sonner-toast][data-type="info"]::after {
    background-color: rgba(255, 255, 255, 0.9);
  }
}

/* Animações personalizadas para a página de login */
@keyframes float-slow {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

@keyframes float-medium {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-15px) rotate(90deg); }
}

@keyframes float-fast {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-10px) rotate(270deg); }
}

@keyframes slide-up {
  0% {
    opacity: 0;
    transform: translateY(50px) scale(0.9);
  }
  100% {
    opacity: 1;
    transform: translateY(0px) scale(1);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(237, 26, 35, 0.5);
  }
  50% {
    box-shadow: 0 0 40px rgba(196, 30, 58, 0.8);
  }
}

.animate-float-slow {
  animation: float-slow 6s ease-in-out infinite;
}

.animate-float-medium {
  animation: float-medium 4s ease-in-out infinite;
}

.animate-float-fast {
  animation: float-fast 3s ease-in-out infinite;
}

.animate-slide-up {
  animation: slide-up 0.8s ease-out;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* Efeito de hover para o card de login */
.login-card:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

/* Gradiente animado para o background */
@keyframes gradient-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.animate-gradient {
  background-size: 200% 200%;
  animation: gradient-shift 8s ease infinite;
}
