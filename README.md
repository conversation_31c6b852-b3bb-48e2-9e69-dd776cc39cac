# javiagens - Sistema de Gestão de Fluxo de Caixa

javiagens é um sistema de gestão financeira desenvolvido para controlar o fluxo de caixa, gerenciar usuários e gerar relatórios financeiros. A aplicação oferece uma interface moderna e responsiva, construída com as melhores práticas de desenvolvimento web.

![javiagens Logo](/public/logo.png)

## Funcionalidades Principais

### Dashboard
- Visão geral das finanças com gráficos e estatísticas
- Resumo de entradas, saídas e saldo atual
- Visualização de tendências financeiras

### Fluxo de Caixa
- Registro de transações (entradas e saídas)
- Categorização de transações
- Filtros por data, tipo e categoria
- Upload de comprovativos
- Visualização detalhada do histórico financeiro

### Usuários
- Gerenciamento de usuários do sistema
- Controle de permissões (administrador e gerente)
- Edição de perfis de usuário

### Relatórios
- Relatório de Fluxo de Caixa Mensal
- Relatório de Transações por Categoria
- Projeções Financeiras
- Exportação de dados

### Configurações
- Personalização do sistema
- Configurações de conta

## Tecnologias Utilizadas

- **Frontend**:
  - React
  - TypeScript
  - Vite (build tool)
  - Tailwind CSS
  - shadcn/ui (componentes)
  - React Router (navegação)
  - React Query (gerenciamento de estado e requisições)
  - Recharts (gráficos)
  - Lucide React (ícones)

- **Backend**:
  - Node.js com Express.js
  - MySQL (banco de dados)
  - JWT (autenticação e autorização)
  - Sistema de upload de arquivos local

## Requisitos do Sistema

### Frontend
- Node.js (versão recomendada: 18.x ou superior)
- npm (gerenciador de pacotes)
- Navegador moderno (Chrome, Firefox, Safari, Edge)

### Backend
- Node.js (versão recomendada: 18.x ou superior)
- MySQL Server (versão 8.0 ou superior)
- npm (gerenciador de pacotes)

## Instalação e Configuração

### Pré-requisitos
- Node.js & npm instalados - [instalar com nvm](https://github.com/nvm-sh/nvm#installing-and-updating)
- MySQL Server instalado e configurado
- Acesso ao banco de dados MySQL

### Passos para Instalação

#### Frontend

1. Clone o repositório:
   ```sh
   git clone <URL_DO_REPOSITÓRIO>
   ```

2. Navegue até o diretório do projeto:
   ```sh
   cd javiagens
   ```

3. Instale as dependências:
   ```sh
   npm install
   ```

4. Configure as variáveis de ambiente:
   - Crie um arquivo `.env` na raiz do projeto
   - Configure a URL da API: `VITE_API_BASE_URL=http://localhost:3001/api`

5. Inicie o servidor de desenvolvimento:
   ```sh
   npm run dev
   ```

#### Backend

1. Navegue até o diretório do backend:
   ```sh
   cd backend
   ```

2. Instale as dependências:
   ```sh
   npm install
   ```

3. Configure o banco de dados MySQL:
   - Crie um banco de dados chamado `javiagens`
   - Execute o script SQL de criação das tabelas (disponível em `mysql_database_schema.sql`)

4. Configure as variáveis de ambiente:
   - Crie um arquivo `.env` no diretório backend
   - Configure as variáveis de conexão com o MySQL:
     ```
     DB_HOST=localhost
     DB_PORT=3306
     DB_USER=seu_usuario
     DB_PASSWORD=sua_senha
     DB_NAME=javiagens
     JWT_SECRET=sua_chave_secreta_jwt
     PORT=3001
     ```

5. Inicie o servidor backend:
   ```sh
   npm start
   ```

6. Acesse a aplicação em seu navegador:
   ```
   http://localhost:5173
   ```

## Estrutura do Projeto

```
javiagens/
├── backend/             # Servidor backend (Node.js + Express)
│   ├── src/             # Código fonte do backend
│   │   ├── routes/      # Rotas da API
│   │   ├── middleware/  # Middlewares de autenticação e validação
│   │   ├── utils/       # Utilitários do backend
│   │   └── server.js    # Servidor principal
│   ├── uploads/         # Arquivos enviados pelos usuários
│   ├── .env             # Variáveis de ambiente do backend
│   └── package.json     # Dependências do backend
├── public/              # Arquivos estáticos do frontend
├── src/                 # Código fonte do frontend
│   ├── api/             # Cliente da API e configurações
│   ├── components/      # Componentes React reutilizáveis
│   ├── hooks/           # Custom hooks
│   ├── integrations/    # Integrações com banco de dados
│   ├── lib/             # Utilitários e funções auxiliares
│   ├── pages/           # Componentes de página
│   ├── App.tsx          # Componente principal da aplicação
│   └── main.tsx         # Ponto de entrada da aplicação
├── .env                 # Variáveis de ambiente do frontend
├── index.html           # Template HTML
├── package.json         # Dependências do frontend
├── tailwind.config.ts   # Configuração do Tailwind CSS
└── vite.config.ts       # Configuração do Vite
```

## Autenticação e Autorização

O sistema utiliza JWT (JSON Web Tokens) para autenticação de usuários e possui dois níveis de acesso:

- **Administrador**: Acesso completo ao sistema, incluindo gerenciamento de usuários
- **Gerente**: Acesso limitado, sem permissão para visualizar certos dados

### Segurança
- Senhas são criptografadas usando bcrypt
- Tokens JWT têm tempo de expiração configurável
- Middleware de autenticação protege rotas sensíveis
- Validação de dados em todas as operações

## Desenvolvimento

### Scripts Disponíveis

- `npm run dev` - Inicia o servidor de desenvolvimento
- `npm run build` - Compila o projeto para produção
- `npm run build:dev` - Compila o projeto para ambiente de desenvolvimento
- `npm run preview` - Visualiza a versão compilada localmente

### Padrões de Código

- Utilize TypeScript para tipagem estática
- Siga os padrões de componentes estabelecidos
- Mantenha a estrutura de arquivos organizada
- Documente alterações significativas

## Suporte

Para suporte ou dúvidas, entre em contato com o desenvolvedor:

- **Desenvolvedor**: Carlos Cesar
- **Email**: [<EMAIL>]

## Licença

Este projeto é proprietário e seu uso é restrito aos termos estabelecidos pelo proprietário.

---

© 2024 javiagens. Todos os direitos reservados.
