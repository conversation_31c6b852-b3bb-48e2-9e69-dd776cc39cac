/**
 * Profile Edit Dialog Component
 *
 * A modal dialog for editing user profile information and changing passwords.
 * Used in the main application layout for authenticated users.
 *
 * Features:
 * - Profile name editing
 * - Password change functionality with strength validation
 * - Real-time password strength indicator
 * - Form validation and error handling
 * - Standardized toast notifications for success/error states
 * - Responsive design with proper accessibility
 *
 * Toast Integration:
 * - Uses toastUtils for consistent notification styling
 * - Success toasts: Green background with white text
 * - Error toasts: Red background with white text
 */

import { useState, useEffect } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Loader2, User, Mail, Eye, EyeOff } from "lucide-react";
import { AuthAPI } from "@/api/auth";
import { DatabaseAPI } from "@/api/database";
import { toastUtils } from "@/lib/toast-utils";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useAuth } from "@/contexts/AuthContext";
import { PasswordStrengthIndicator, calculatePasswordStrength } from "@/components/ui/password-strength-indicator";

interface ProfileData {
  id: string;
  name: string;
  email: string;
}

interface ProfileEditDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

export const ProfileEditDialog = ({ isOpen, onClose }: ProfileEditDialogProps) => {
  const [profileData, setProfileData] = useState<ProfileData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [formData, setFormData] = useState({
    name: "",
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  });
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const queryClient = useQueryClient();
  const { refreshUser } = useAuth();

  // Busca dados do perfil do usuário atual
  useEffect(() => {
    const fetchUserProfile = async () => {
      setIsLoading(true);
      try {
        const session = await AuthAPI.getSession();

        if (!session?.user) {
          toastUtils.error("Sessão expirada", "Por favor, faça login novamente.");
          return;
        }

        // Usar os dados do usuário da sessão atual
        const profile = {
          id: session.user.id,
          name: session.user.name,
          email: session.user.email
        };

        if (!profile) throw new Error('Perfil não encontrado');

        setProfileData(profile);
        setFormData(prev => ({
          ...prev,
          name: profile.name || "",
        }));
      } catch (error) {
        console.error("Erro ao carregar perfil:", error);
        toastUtils.error("Erro ao carregar perfil", "Não foi possível carregar os dados do perfil.");
      } finally {
        setIsLoading(false);
      }
    };

    if (isOpen) {
      fetchUserProfile();
    }
  }, [isOpen]);

  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Mutação para atualizar perfil e senha
  const updateProfileMutation = useMutation({
    mutationFn: async () => {
      try {
        // Validar senhas se o usuário está tentando alterar a senha
        if (formData.newPassword) {
          if (!formData.currentPassword) {
            throw new Error("Senha atual é obrigatória para alterar a senha.");
          }

          if (formData.newPassword !== formData.confirmPassword) {
            throw new Error("As senhas não coincidem.");
          }

          // Check password strength
          const passwordStrength = calculatePasswordStrength(formData.newPassword);
          if (passwordStrength.score < 2) {
            throw new Error("A senha é muito fraca. Por favor, use uma senha mais forte.");
          }

          // Atualizar senha
          await AuthAPI.changePassword({
            currentPassword: formData.currentPassword,
            newPassword: formData.newPassword,
            confirmPassword: formData.confirmPassword,
          });
        }

        // Atualizar dados do perfil
        if (profileData?.id) {
          await DatabaseAPI.profiles.update(profileData.id, {
            name: formData.name,
          });
        }
      } catch (error: any) {
        throw new Error(error.message || 'Erro ao atualizar perfil');
      }
    },
    onSuccess: async () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      // Refresh auth context to update sidebar immediately
      await refreshUser();
      toastUtils.success("Perfil atualizado com sucesso!");
      onClose();

      // Reset form
      setFormData({
        name: "",
        currentPassword: "",
        newPassword: "",
        confirmPassword: "",
      });
    },
    onError: (error: Error) => {
      console.error(error);
      toastUtils.error("Erro ao atualizar perfil", error.message);
    }
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    updateProfileMutation.mutate();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[450px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Editar Meu Perfil</DialogTitle>
          <DialogDescription>
            Atualize suas informações pessoais e senha.
          </DialogDescription>
        </DialogHeader>

        {isLoading ? (
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-javiagens-red" />
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="space-y-6 py-4">
            {/* Informações Pessoais */}
            <div className="space-y-4">
              <h3 className="text-sm font-medium text-muted-foreground">Informações Pessoais</h3>

              <div className="space-y-2">
                <Label htmlFor="name" className="flex items-center gap-2">
                  <User className="h-4 w-4" />
                  Nome <span className="text-red-500 ml-1">*</span>
                </Label>
                <Input
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  placeholder="Seu nome completo"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label className="flex items-center gap-2">
                  <Mail className="h-4 w-4" />
                  Email
                </Label>
                <Input
                  value={profileData?.email || ""}
                  disabled
                  className="bg-muted"
                />
                <p className="text-xs text-muted-foreground">O email não pode ser alterado.</p>
              </div>
            </div>

            {/* Alteração de Senha */}
            <div className="space-y-4 pt-2 border-t">
              <h3 className="text-sm font-medium text-muted-foreground pt-2">Alteração de Senha</h3>

              {/* Current Password Field */}
              <div className="space-y-2">
                <Label htmlFor="currentPassword">Senha Atual</Label>
                <div className="relative">
                  <Input
                    id="currentPassword"
                    name="currentPassword"
                    type={showCurrentPassword ? "text" : "password"}
                    value={formData.currentPassword}
                    onChange={handleChange}
                    placeholder="Digite sua senha atual"
                    className="pr-10"
                  />
                  <button
                    type="button"
                    onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
                  >
                    {showCurrentPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                  </button>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="newPassword">Nova Senha</Label>
                  <div className="relative">
                    <Input
                      id="newPassword"
                      name="newPassword"
                      type={showNewPassword ? "text" : "password"}
                      value={formData.newPassword}
                      onChange={handleChange}
                      placeholder="Digite a nova senha"
                      className="pr-10"
                    />
                    <button
                      type="button"
                      onClick={() => setShowNewPassword(!showNewPassword)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
                    >
                      {showNewPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                    </button>
                  </div>
                  {/* Password Strength Indicator */}
                  {formData.newPassword && (
                    <PasswordStrengthIndicator
                      password={formData.newPassword}
                      showFeedback={true}
                      language="pt"
                    />
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="confirmPassword">Confirmar Nova Senha</Label>
                  <div className="relative">
                    <Input
                      id="confirmPassword"
                      name="confirmPassword"
                      type={showConfirmPassword ? "text" : "password"}
                      value={formData.confirmPassword}
                      onChange={handleChange}
                      placeholder="Confirme a nova senha"
                      className="pr-10"
                    />
                    <button
                      type="button"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
                    >
                      {showConfirmPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                    </button>
                  </div>
                </div>
              </div>

              <p className="text-xs text-muted-foreground">
                Deixe os campos de senha em branco se não desejar alterá-la.
              </p>
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
              >
                Cancelar
              </Button>
              <Button
                type="submit"
                className="bg-javiagens-red hover:bg-javiagens-dark-red"
                disabled={updateProfileMutation.isPending}
              >
                {updateProfileMutation.isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Salvando...
                  </>
                ) : (
                  "Salvar Alterações"
                )}
              </Button>
            </DialogFooter>
          </form>
        )}
      </DialogContent>
    </Dialog>
  );
};
