
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { LogIn, Eye, EyeOff, AlertCircle } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useAuth } from "@/contexts/AuthContext";
import { useFormPersistence } from "@/hooks/useFormPersistence";
import { ButtonLoading } from "@/components/ui/loading-spinner";
import { toast } from "sonner";

interface LoginFormData {
  email: string;
  password: string;
}

const Login = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();
  const { login, isLoading } = useAuth();

  // Hook para persistir dados do formulário (exceto senha)
  const {
    formData,
    updateField,
    clearSensitiveFields,
    markAsSuccess
  } = useFormPersistence<LoginFormData>(
    { email: "", password: "" },
    {
      key: "login",
      excludeFields: ["password"], // Não persistir senha por segurança
      clearOnSuccess: true
    }
  );

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    try {
      await login(formData.email, formData.password);
      markAsSuccess(); // Limpa dados persistidos após sucesso
      navigate("/");
    } catch (error: any) {
      console.error("Erro ao fazer login:", error);
      setError(error.message || "Erro ao fazer login. Verifique suas credenciais.");
      // Limpa apenas a senha após erro, mantém email
      clearSensitiveFields();
    }
  };

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Background animado com gradiente e elementos visuais */}
      <div className="absolute inset-0 bg-gradient-to-br from-javiagens-red via-javiagens-light-red to-javiagens-dark-red animate-gradient">
        {/* Elementos flutuantes animados */}
        <div className="absolute top-20 left-10 w-20 h-20 bg-white/10 rounded-full animate-float-slow"></div>
        <div className="absolute top-40 right-20 w-16 h-16 bg-white/5 rounded-full animate-float-medium"></div>
        <div className="absolute bottom-32 left-20 w-24 h-24 bg-white/10 rounded-full animate-float-fast"></div>
        <div className="absolute bottom-20 right-10 w-12 h-12 bg-white/15 rounded-full animate-float-slow"></div>

        {/* Padrão de ondas */}
        <div className="absolute bottom-0 left-0 w-full h-32 bg-gradient-to-t from-white/20 to-transparent"></div>

        {/* Ícones de viagem flutuantes */}
        <div className="absolute top-16 right-16 text-white/20 animate-float-medium">
          <svg width="32" height="32" viewBox="0 0 24 24" fill="currentColor">
            <path d="M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z"/>
          </svg>
        </div>

        <div className="absolute bottom-40 left-16 text-white/15 animate-float-slow">
          <svg width="28" height="28" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
          </svg>
        </div>

        <div className="absolute top-32 left-32 text-white/10 animate-float-fast">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
        </div>
      </div>

      {/* Conteúdo principal */}
      <div className="relative z-10 min-h-screen flex items-center justify-center px-4">
        <div className="w-full max-w-md">
          {/* Card com animação de entrada e efeitos visuais */}
          <Card className="border-none shadow-2xl backdrop-blur-sm bg-white/95 transform transition-all duration-500 login-card animate-slide-up">
            <CardContent className="pt-8 pb-6">
            {/* Título dentro do card com padding adequado e ícone de viagem */}
            <div className="text-center mb-8">
              <div className="flex justify-center mb-4">
                <div className="w-16 h-16 bg-gradient-to-br from-javiagens-red to-javiagens-dark-red rounded-full flex items-center justify-center shadow-lg animate-pulse">
                  <svg width="32" height="32" viewBox="0 0 24 24" fill="white">
                    <path d="M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z"/>
                  </svg>
                </div>
              </div>
              <h1 className="text-3xl font-bold mb-2 bg-gradient-to-r from-javiagens-red to-javiagens-dark-red bg-clip-text text-transparent">
                Bem-vindo à JAVIAGENS
              </h1>
              <p className="text-muted-foreground">Sistema de Gestão de Fluxo de Caixa</p>
              <p className="text-sm text-muted-foreground/80 mt-1">Sua jornada financeira começa aqui</p>
            </div>
            {error && (
              <Alert variant="destructive" className="mb-4">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <form onSubmit={handleLogin} className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Email</label>
                <div className="relative">
                  <Input
                    type="email"
                    placeholder="<EMAIL>"
                    className="pl-10 transition-all duration-300 focus:ring-2 focus:ring-javiagens-red focus:border-transparent hover:shadow-md"
                    required
                    value={formData.email}
                    onChange={(e) => updateField('email', e.target.value)}
                    disabled={isLoading}
                  />
                  <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <rect width="20" height="16" x="2" y="4" rx="2" />
                      <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7" />
                    </svg>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Senha</label>
                <div className="relative">
                  <Input
                    type={showPassword ? "text" : "password"}
                    placeholder="••••••••"
                    className="pl-10 pr-10 transition-all duration-300 focus:ring-2 focus:ring-javiagens-light-red focus:border-transparent hover:shadow-md"
                    required
                    value={formData.password}
                    onChange={(e) => updateField('password', e.target.value)}
                    disabled={isLoading}
                  />
                  <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <rect width="18" height="11" x="3" y="11" rx="2" ry="2" />
                      <path d="M7 11V7a5 5 0 0 1 10 0v4" />
                    </svg>
                  </div>
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
                  >
                    {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                  </button>
                </div>
              </div>

              <Button
                type="submit"
                className="w-full bg-gradient-to-r from-javiagens-red to-javiagens-dark-red hover:from-javiagens-light-red hover:to-javiagens-red transform transition-all duration-300 hover:shadow-lg"
                disabled={isLoading}
              >
                <ButtonLoading
                  isLoading={isLoading}
                  loadingText="Entrando..."
                >
                  <div className="flex items-center gap-2">
                    <LogIn className="h-4 w-4" />
                    <span>Entrar</span>
                  </div>
                </ButtonLoading>
              </Button>

              <div className="text-center text-sm">
                <a
                  href="#"
                  className="text-javiagens-red hover:underline"
                  onClick={(e) => {
                    e.preventDefault();
                    toast.info("Entre em contato com o administrador do sistema para recuperar sua senha.");
                  }}
                >
                  Esqueceu sua senha?
                </a>
              </div>
            </form>
          </CardContent>
        </Card>
        </div>
      </div>
    </div>
  );
};

export default Login;
