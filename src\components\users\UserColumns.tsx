
import { Column } from "@/components/DataTable";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>, <PERSON>r<PERSON><PERSON><PERSON>, UserX, Trash2 } from "lucide-react";
import { UserAvatar } from "./UserAvatar";
import { UserStatusBadge } from "./UserStatusBadge";
import { User } from "./EditUserForm";


interface UserColumnsProps {
  onEdit: (user: User) => void;
  onToggleStatus: (user: User) => void;
  onDelete: (user: User) => void;
  userRole?: string | null;
}

export const getUserColumns = ({ onEdit, onToggleStatus, onDelete, userRole }: UserColumnsProps): Column<User>[] => [
  {
    header: "USUÁRIO",
    accessorKey: (row: User) => (
      <div className="flex items-center gap-3">
        <UserAvatar name={row.name} />
        <div>
          <div className="font-medium">{row.name}</div>
          <div className="text-sm text-muted-foreground">{row.email}</div>
        </div>
      </div>
    ),
    className: "min-w-[250px]",
  },
  {
    header: "PERFIL",
    accessorKey: (row: User) => row.role,
    className: "text-center",
  },
  {
    header: "STATUS",
    accessorKey: (row: User) => <UserStatusBadge status={row.status} />,
    className: "text-center",
  },
  {
    header: "TELEFONE",
    accessorKey: (row: User) => row.phone || "Não informado",
    className: "text-center",
  },
  {
    header: "ÚLTIMO LOGIN",
    accessorKey: (row: User) => {
      if (!row.last_login) return "Nunca";
      return new Date(row.last_login).toLocaleString('pt-BR');
    },
    className: "text-center",
  },
  {
    header: "AÇÕES",
    accessorKey: (row: User) => {
      const isAdmin = userRole === 'administrador';

      return (
        <div className="flex justify-center space-x-2">
          {isAdmin ? (
            <>
              <Button
                variant="outline"
                size="icon"
                className="h-8 w-8"
                onClick={(e) => {
                  e.stopPropagation();
                  onEdit(row);
                }}
                title="Editar usuário"
              >
                <Pencil className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="icon"
                className={`h-8 w-8 ${row.status ? 'text-orange-600 hover:bg-orange-50' : 'text-green-600 hover:bg-green-50'}`}
                onClick={(e) => {
                  e.stopPropagation();
                  onToggleStatus(row);
                }}
                title={row.status ? "Desativar usuário" : "Ativar usuário"}
              >
                {row.status ? <UserX className="h-4 w-4" /> : <UserCheck className="h-4 w-4" />}
              </Button>
              <Button
                variant="outline"
                size="icon"
                className="h-8 w-8 text-red-600 hover:bg-red-50 hover:border-red-200"
                onClick={(e) => {
                  e.stopPropagation();
                  onDelete(row);
                }}
                title="Deletar usuário permanentemente"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </>
          ) : (
            <span className="text-sm text-muted-foreground">Visualização apenas</span>
          )}
        </div>
      );
    },
    className: "w-[160px] text-center",
  },
];
