
import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Limita um valor percentual entre -100% e +100%
 * @param value O valor percentual a ser limitado
 * @returns Um objeto com o valor limitado e um booleano indicando se o limite foi aplicado
 */
export function capPercentage(value: number): { value: number, capped: boolean } {
  const capped = Math.abs(value) > 100;
  const cappedValue = value > 0
    ? Math.min(value, 100)
    : Math.max(value, -100);

  return { value: cappedValue, capped };
}

/**
 * Realiza a limpeza completa do estado de autenticação do sistema MySQL/JWT
 */
export function cleanupAuthState() {
  // Remover tokens de autenticação do sistema atual
  localStorage.removeItem('auth_token');
  localStorage.removeItem('auth.user');
}
