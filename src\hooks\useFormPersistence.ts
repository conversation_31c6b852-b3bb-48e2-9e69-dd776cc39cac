// Hook customizado para persistência de dados de formulário
// Resolve o problema de perda de dados no formulário de login após erros

import { useState, useEffect, useCallback } from 'react';

interface UseFormPersistenceOptions {
  key: string; // Chave única para identificar o formulário
  excludeFields?: string[]; // Campos que não devem ser persistidos (ex: senha)
  clearOnSuccess?: boolean; // Limpar dados após sucesso
}

interface FormData {
  [key: string]: any;
}

/**
 * Hook para persistir dados de formulário no localStorage
 * Útil para manter dados preenchidos após erros de validação
 */
export const useFormPersistence = <T extends FormData>(
  initialData: T,
  options: UseFormPersistenceOptions
) => {
  const { key, excludeFields = [], clearOnSuccess = true } = options;
  const storageKey = `form_${key}`;

  // Estado do formulário
  const [formData, setFormData] = useState<T>(initialData);

  // Carregar dados persistidos ao montar o componente
  useEffect(() => {
    try {
      const savedData = localStorage.getItem(storageKey);
      if (savedData) {
        const parsedData = JSON.parse(savedData);
        setFormData(prev => ({ ...prev, ...parsedData }));
      }
    } catch (error) {
      console.warn('Erro ao carregar dados persistidos:', error);
    }
  }, [storageKey]);

  // Salvar dados no localStorage (excluindo campos sensíveis)
  const persistData = useCallback((data: Partial<T>) => {
    try {
      const dataToSave = { ...data };
      
      // Remover campos excluídos (como senhas)
      excludeFields.forEach(field => {
        delete dataToSave[field];
      });

      localStorage.setItem(storageKey, JSON.stringify(dataToSave));
    } catch (error) {
      console.warn('Erro ao persistir dados:', error);
    }
  }, [storageKey, excludeFields]);

  // Atualizar campo específico
  const updateField = useCallback((field: keyof T, value: any) => {
    setFormData(prev => {
      const newData = { ...prev, [field]: value };
      persistData(newData);
      return newData;
    });
  }, [persistData]);

  // Atualizar múltiplos campos
  const updateFields = useCallback((updates: Partial<T>) => {
    setFormData(prev => {
      const newData = { ...prev, ...updates };
      persistData(newData);
      return newData;
    });
  }, [persistData]);

  // Limpar dados persistidos
  const clearPersistedData = useCallback(() => {
    try {
      localStorage.removeItem(storageKey);
      setFormData(initialData);
    } catch (error) {
      console.warn('Erro ao limpar dados persistidos:', error);
    }
  }, [storageKey, initialData]);

  // Limpar apenas campos sensíveis (manter outros dados)
  const clearSensitiveFields = useCallback(() => {
    setFormData(prev => {
      const newData = { ...prev };
      excludeFields.forEach(field => {
        newData[field] = initialData[field];
      });
      persistData(newData);
      return newData;
    });
  }, [excludeFields, initialData, persistData]);

  // Marcar como sucesso (limpa dados se configurado)
  const markAsSuccess = useCallback(() => {
    if (clearOnSuccess) {
      clearPersistedData();
    }
  }, [clearOnSuccess, clearPersistedData]);

  return {
    formData,
    updateField,
    updateFields,
    clearPersistedData,
    clearSensitiveFields,
    markAsSuccess,
    setFormData: updateFields
  };
};
