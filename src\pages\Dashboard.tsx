import { PageHeader } from "@/components/PageHeader";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  Bar,
  Legend
} from "recharts";
import { useDashboardData } from "@/hooks/useDashboardData";
import { DashboardStats } from "@/components/DashboardStats";
import { Skeleton } from "@/components/ui/skeleton";
import { useUsers } from "@/hooks/useUsers";

const Dashboard = () => {
  const { annualData, compareData, summary, isLoading: isLoadingDashboard } = useDashboardData();
  const { activeUsersCount, activeUsersPercentChange, isLoading: isLoadingUsers } = useUsers();

  const isLoading = isLoadingDashboard || isLoadingUsers;

  return (
    <div className="animate-fade-in">
      <PageHeader title="Dashboard" />

      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
          {Array(4).fill(0).map((_, i) => (
            <Skeleton key={i} className="h-32" />
          ))}
        </div>
      ) : (
        <DashboardStats
          totalEntradas={summary.totalEntradas}
          totalSaidas={summary.totalSaidas}
          saldoAtual={summary.saldoAtual}
          activeUsersCount={activeUsersCount}
          percentChanges={{
            ...summary.percentChanges,
            users: activeUsersPercentChange
          }}
        />
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <Card>
          <CardHeader>
            <CardTitle>Fluxo de Caixa Anual</CardTitle>
            <CardDescription>Visão geral do desempenho financeiro</CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <Skeleton className="h-[300px]" />
            ) : annualData.length > 0 ? (
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={annualData}
                    margin={{
                      top: 5,
                      right: 20,
                      left: 20,
                      bottom: 5,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" opacity={0.2} />
                    <XAxis dataKey="name" />
                    <YAxis
                      tickFormatter={(value) => `${value.toLocaleString()} kz`}
                      width={80}
                    />
                    <Tooltip
                      formatter={(value: number) =>
                        `${value.toLocaleString()} kz`
                      }
                    />
                    <Line
                      type="monotone"
                      dataKey="valor"
                      stroke="#a94795"
                      strokeWidth={3}
                      activeDot={{ r: 8 }}
                      dot={{ r: 4 }}
                      fill="url(#colorUv)"
                    />
                    <defs>
                      <linearGradient id="colorUv" x1="0" y1="0" x2="0" y2="1">
                        <stop
                          offset="5%"
                          stopColor="#a94795"
                          stopOpacity={0.8}
                        />
                        <stop
                          offset="95%"
                          stopColor="#a94795"
                          stopOpacity={0.1}
                        />
                      </linearGradient>
                    </defs>
                  </LineChart>
                </ResponsiveContainer>
              </div>
            ) : (
              <div className="h-[300px] flex items-center justify-center">
                <p className="text-muted-foreground">Sem dados disponíveis</p>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Comparativo de Entradas e Saídas</CardTitle>
            <CardDescription>Análise dos últimos 6 meses</CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <Skeleton className="h-[300px]" />
            ) : compareData.length > 0 ? (
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={compareData}
                    margin={{
                      top: 5,
                      right: 20,
                      left: 20,
                      bottom: 5,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" opacity={0.2} />
                    <XAxis dataKey="name" />
                    <YAxis
                      tickFormatter={(value) => `${value.toLocaleString()} kz`}
                      width={80}
                    />
                    <Tooltip
                      formatter={(value: number) =>
                        `${value.toLocaleString()} kz`
                      }
                    />
                    <Legend />
                    <Bar
                      dataKey="entradas"
                      name="Entradas"
                      fill="#22c55e"
                      radius={[4, 4, 0, 0]}
                    />
                    <Bar
                      dataKey="saidas"
                      name="Saídas"
                      fill="#ef4444"
                      radius={[4, 4, 0, 0]}
                    />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            ) : (
              <div className="h-[300px] flex items-center justify-center">
                <p className="text-muted-foreground">Sem dados disponíveis</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Dashboard;
