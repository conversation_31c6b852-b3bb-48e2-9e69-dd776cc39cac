// MySQL Database Types for javiagens

export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      categories: {
        Row: {
          created_at: string
          created_by: string
          id: string
          name: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          created_by: string
          id?: string
          name: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          created_by?: string
          id?: string
          name?: string
          updated_at?: string
        }
        Relationships: []
      }
      profiles: {
        Row: {
          avatar: string | null
          created_at: string
          email: string
          id: string
          last_login: string | null
          name: string
          phone: string | null
          role: "administrador" | "gerente"
          status: boolean
        }
        Insert: {
          avatar?: string | null
          created_at?: string
          email: string
          id: string
          last_login?: string | null
          name: string
          phone?: string | null
          role?: "administrador" | "gerente"
          status?: boolean
        }
        Update: {
          avatar?: string | null
          created_at?: string
          email?: string
          id?: string
          last_login?: string | null
          name?: string
          phone?: string | null
          role?: "administrador" | "gerente"
          status?: boolean
        }
        Relationships: []
      }
      subcategories: {
        Row: {
          category_id: string
          created_at: string
          created_by: string
          id: string
          name: string
          updated_at: string
        }
        Insert: {
          category_id: string
          created_at?: string
          created_by: string
          id?: string
          name: string
          updated_at?: string
        }
        Update: {
          category_id?: string
          created_at?: string
          created_by?: string
          id?: string
          name?: string
          updated_at?: string
        }
        Relationships: []
      }
      transactions: {
        Row: {
          amount: number
          category_id: string | null
          comprovativo_url: string | null
          created_at: string
          created_by: string
          date: string
          description: string
          id: string
          subcategory_id: string | null
          type: "entrada" | "saida"
          updated_at: string
        }
        Insert: {
          amount: number
          category_id?: string | null
          comprovativo_url?: string | null
          created_at?: string
          created_by: string
          date: string
          description: string
          id?: string
          subcategory_id?: string | null
          type: "entrada" | "saida"
          updated_at?: string
        }
        Update: {
          amount?: number
          category_id?: string | null
          comprovativo_url?: string | null
          created_at?: string
          created_by?: string
          date?: string
          description?: string
          id?: string
          subcategory_id?: string | null
          type?: "entrada" | "saida"
          updated_at?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      app_role: "administrador" | "gerente"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

// Simplified type exports for MySQL compatibility
export type Tables<T extends keyof Database["public"]["Tables"]> = Database["public"]["Tables"][T]["Row"]
export type TablesInsert<T extends keyof Database["public"]["Tables"]> = Database["public"]["Tables"][T]["Insert"]
export type TablesUpdate<T extends keyof Database["public"]["Tables"]> = Database["public"]["Tables"][T]["Update"]
export type Enums<T extends keyof Database["public"]["Enums"]> = Database["public"]["Enums"][T]
