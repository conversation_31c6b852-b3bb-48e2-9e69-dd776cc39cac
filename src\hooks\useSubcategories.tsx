
import { useState, useEffect } from "react";
import { toastUtils } from "@/lib/toast-utils";
import { Subcategory } from "@/features/configuracoes/SubcategoryTable";
import { httpMethods, API_ENDPOINTS } from "@/api/config";

export function useSubcategories() {
  const [subcategories, setSubcategories] = useState<Subcategory[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const fetchSubcategories = async () => {
    try {
      setIsLoading(true);

      // Use the httpMethods to make the API call with proper base URL and auth headers
      const response = await httpMethods.get<Subcategory[]>(API_ENDPOINTS.subcategories.list, { limit: 100 });

      if (!response.success) {
        throw new Error(response.error || 'Failed to fetch subcategories');
      }

      // The backend already returns category_name from the JOIN
      setSubcategories(response.data || []);
    } catch (error: any) {
      console.error('Erro ao buscar subcategorias:', error);
      toastUtils.error("Erro", error.message || "Não foi possível carregar as subcategorias.");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchSubcategories();
  }, []);

  return {
    subcategories,
    isLoading,
    fetchSubcategories
  };
}
