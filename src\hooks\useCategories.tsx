
import { useState, useEffect } from "react";
import { DatabaseAPI } from "@/api/database";
import { toastUtils } from "@/lib/toast-utils";
import { Category } from "@/features/configuracoes/CategoryTable";

export function useCategories() {
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Busca todas as categorias do sistema
  const fetchCategories = async () => {
    try {
      setIsLoading(true);
      const data = await DatabaseAPI.categories
        .select('*')
        .order('name')
        .execute();

      setCategories(data || []);
    } catch (error: any) {
      console.error('Erro ao buscar categorias:', error);
      toastUtils.error("Erro", error.message || "Não foi possível carregar as categorias.");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchCategories();
  }, []);

  return {
    categories,
    isLoading,
    fetchCategories
  };
}
