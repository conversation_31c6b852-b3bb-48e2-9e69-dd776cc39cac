
import { useState, useEffect } from "react";
import { PageHeader } from "@/components/PageHeader";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { useCategories } from "@/hooks/useCategories";
import { useSubcategories } from "@/hooks/useSubcategories";
import { CategoryTable, Category } from "@/features/configuracoes/CategoryTable";
import { SubcategoryTable, Subcategory } from "@/features/configuracoes/SubcategoryTable";
import { CategoryDialogs } from "@/features/configuracoes/CategoryDialogs";
import { SubcategoryDialogs } from "@/features/configuracoes/SubcategoryDialogs";

const Configuracoes = () => {
  const [activeTab, setActiveTab] = useState("categorias");
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);
  const [selectedSubcategory, setSelectedSubcategory] = useState<Subcategory | null>(null);
  
  const [isNewCategoryOpen, setIsNewCategoryOpen] = useState(false);
  const [isEditCategoryOpen, setIsEditCategoryOpen] = useState(false);
  const [isDeleteCategoryOpen, setIsDeleteCategoryOpen] = useState(false);
  
  const [isNewSubcategoryOpen, setIsNewSubcategoryOpen] = useState(false);
  const [isEditSubcategoryOpen, setIsEditSubcategoryOpen] = useState(false);
  const [isDeleteSubcategoryOpen, setIsDeleteSubcategoryOpen] = useState(false);

  const { categories, isLoading: categoriesLoading, fetchCategories } = useCategories();
  const { subcategories, isLoading: subcategoriesLoading, fetchSubcategories } = useSubcategories();
  
  // Handlers para abrir os modais
  const handleNewCategory = () => {
    setIsNewCategoryOpen(true);
  };
  
  const handleEditCategory = (category: Category) => {
    setSelectedCategory(category);
    setIsEditCategoryOpen(true);
  };
  
  const handleDeleteCategory = (category: Category) => {
    setSelectedCategory(category);
    setIsDeleteCategoryOpen(true);
  };
  
  const handleNewSubcategory = () => {
    setIsNewSubcategoryOpen(true);
  };
  
  const handleEditSubcategory = (subcategory: Subcategory) => {
    setSelectedSubcategory(subcategory);
    setIsEditSubcategoryOpen(true);
  };
  
  const handleDeleteSubcategory = (subcategory: Subcategory) => {
    setSelectedSubcategory(subcategory);
    setIsDeleteSubcategoryOpen(true);
  };
  
  // Recarrega os dados quando a tab ativa muda
  useEffect(() => {
    if (activeTab === "categorias") {
      fetchCategories();
    } else if (activeTab === "subcategorias") {
      fetchSubcategories();
    }
  }, [activeTab]);

  return (
    <div className="animate-fade-in">
      <PageHeader title="Configurações" />
      
      <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-8">
        <TabsList className="mb-4">
          <TabsTrigger value="categorias">Categorias</TabsTrigger>
          <TabsTrigger value="subcategorias">Subcategorias</TabsTrigger>
        </TabsList>
        
        <TabsContent value="categorias">
          <CategoryTable
            categories={categories}
            isLoading={categoriesLoading}
            handleNewCategory={handleNewCategory}
            handleEditCategory={handleEditCategory}
            handleDeleteCategory={handleDeleteCategory}
          />
        </TabsContent>
        
        <TabsContent value="subcategorias">
          <SubcategoryTable
            subcategories={subcategories}
            categories={categories}
            isLoading={subcategoriesLoading}
            handleNewSubcategory={handleNewSubcategory}
            handleEditSubcategory={handleEditSubcategory}
            handleDeleteSubcategory={handleDeleteSubcategory}
          />
        </TabsContent>
      </Tabs>
      
      {/* Category Dialogs */}
      <CategoryDialogs
        refreshCategories={fetchCategories}
        isNewCategoryOpen={isNewCategoryOpen}
        setIsNewCategoryOpen={setIsNewCategoryOpen}
        isEditCategoryOpen={isEditCategoryOpen}
        setIsEditCategoryOpen={setIsEditCategoryOpen}
        isDeleteCategoryOpen={isDeleteCategoryOpen}
        setIsDeleteCategoryOpen={setIsDeleteCategoryOpen}
        selectedCategory={selectedCategory}
      />
      
      {/* Subcategory Dialogs */}
      <SubcategoryDialogs
        refreshSubcategories={fetchSubcategories}
        categories={categories}
        isNewSubcategoryOpen={isNewSubcategoryOpen}
        setIsNewSubcategoryOpen={setIsNewSubcategoryOpen}
        isEditSubcategoryOpen={isEditSubcategoryOpen}
        setIsEditSubcategoryOpen={setIsEditSubcategoryOpen}
        isDeleteSubcategoryOpen={isDeleteSubcategoryOpen}
        setIsDeleteSubcategoryOpen={setIsDeleteSubcategoryOpen}
        selectedSubcategory={selectedSubcategory}
      />
    </div>
  );
};

export default Configuracoes;
