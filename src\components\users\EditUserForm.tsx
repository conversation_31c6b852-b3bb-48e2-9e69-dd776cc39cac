
import { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DialogFooter } from "@/components/ui/dialog";
import { Loader2, Eye, EyeOff, Key } from "lucide-react";
import { DatabaseAPI } from "@/api/database";
import { toastUtils } from "@/lib/toast-utils";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { PasswordStrengthIndicator, calculatePasswordStrength } from "@/components/ui/password-strength-indicator";
import { Separator } from "@/components/ui/separator";

// Função para formatar número de telefone angolano
const formatAngolanPhone = (value: string): string => {
  // Remove todos os caracteres não numéricos
  const numbers = value.replace(/\D/g, '');

  // Se começar com 244, remove para evitar duplicação
  const cleanNumbers = numbers.startsWith('244') ? numbers.slice(3) : numbers;

  // Limita a 9 dígitos
  const limitedNumbers = cleanNumbers.slice(0, 9);

  // Aplica a formatação (+244) xxx xxx xxx
  if (limitedNumbers.length === 0) return '';
  if (limitedNumbers.length <= 3) return `(+244) ${limitedNumbers}`;
  if (limitedNumbers.length <= 6) return `(+244) ${limitedNumbers.slice(0, 3)} ${limitedNumbers.slice(3)}`;
  return `(+244) ${limitedNumbers.slice(0, 3)} ${limitedNumbers.slice(3, 6)} ${limitedNumbers.slice(6)}`;
};

export interface User {
  id: string;
  name: string;
  email: string;
  role: "administrador" | "gerente";
  status: boolean;
  phone: string | null;
  last_login: string | null;
  avatar: string;
}

interface EditUserFormProps {
  user: Partial<User>;
  onClose: () => void;
}

export const EditUserForm = ({ user, onClose }: EditUserFormProps) => {
  const [editUserForm, setEditUserForm] = useState<Partial<User>>(user);
  const queryClient = useQueryClient();
  
  // Update local form state when user prop changes
  useEffect(() => {
    setEditUserForm(user);
  }, [user]);

  // Password reset state
  const [showPasswordReset, setShowPasswordReset] = useState(false);
  const [passwordData, setPasswordData] = useState({
    newPassword: "",
    confirmPassword: "",
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  
  // Mutação para atualizar usuário
  const updateUserMutation = useMutation({
    mutationFn: async (userData: Partial<User>) => {
      try {
        if (!userData.id) throw new Error('ID do usuário é obrigatório');

        // Only send fields that have values
        const updateData: any = {};
        if (userData.name) updateData.name = userData.name;
        if (userData.phone) updateData.phone = userData.phone;
        if (userData.role) updateData.role = userData.role;

        await DatabaseAPI.users.update(userData.id, updateData);
      } catch (error: any) {
        throw new Error(error.message || 'Erro ao atualizar usuário');
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      onClose();
      toastUtils.user.updated();
    },
    onError: (error) => {
      console.error(error);
      toastUtils.user.error("atualizar", error.message);
    }
  });

  // Password reset mutation
  const resetPasswordMutation = useMutation({
    mutationFn: async (data: { userId: string; newPassword: string }) => {
      try {
        await DatabaseAPI.users.resetPassword(data.userId, data.newPassword);
      } catch (error: any) {
        throw new Error(error.message || 'Erro ao redefinir senha');
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      toastUtils.user.passwordChanged();
      // Reset password form
      setPasswordData({ newPassword: "", confirmPassword: "" });
      setShowPasswordReset(false);
    },
    onError: (error) => {
      console.error(error);

      // Check if this is the "cannot reset own password" error
      if (error.message.includes('Use change-password endpoint') ||
          error.message.includes('CANNOT_RESET_OWN_PASSWORD') ||
          error.message.includes('Dados inválidos')) {
        toastUtils.warning(
          "Não é possível redefinir sua própria senha aqui",
          "Use a opção 'Editar Perfil' no menu do usuário para alterar sua senha."
        );
      } else {
        toastUtils.user.error("redefinir senha", error.message);
      }
    }
  });

  const handleEditUserSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    updateUserMutation.mutate(editUserForm);
  };

  const handlePasswordReset = () => {

    if (passwordData.newPassword !== passwordData.confirmPassword) {
      toastUtils.error("Erro de validação", "As senhas não coincidem!");
      return;
    }

    // Check password strength
    const passwordStrength = calculatePasswordStrength(passwordData.newPassword);
    if (passwordStrength.score < 2) {
      toastUtils.error("Senha muito fraca", "Por favor, use uma senha mais forte.");
      return;
    }

    if (!user.id) {
      toastUtils.error("Erro interno", "ID do usuário não encontrado!");
      return;
    }

    resetPasswordMutation.mutate({
      userId: user.id,
      newPassword: passwordData.newPassword
    });
  };

  return (
    <form onSubmit={handleEditUserSubmit}>
      <div className="grid gap-4 py-4">
        <div className="space-y-2">
          <label className="text-sm font-medium">
            Nome <span className="text-red-500">*</span>
          </label>
          <Input
            placeholder="Nome completo do usuário"
            value={editUserForm.name || ""}
            onChange={(e) => setEditUserForm({...editUserForm, name: e.target.value})}
            required
          />
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium">
            Email <span className="text-red-500">*</span>
          </label>
          <Input
            type="email"
            placeholder="<EMAIL>"
            value={editUserForm.email || ""}
            onChange={(e) => setEditUserForm({...editUserForm, email: e.target.value})}
            required
            disabled
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Telefone</label>
            <Input
              placeholder="(+244) xxx xxx xxx"
              value={editUserForm.phone || ""}
              onChange={(e) => {
                const formatted = formatAngolanPhone(e.target.value);
                setEditUserForm({...editUserForm, phone: formatted});
              }}
            />
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">
              Perfil <span className="text-red-500">*</span>
            </label>
            <Select 
              value={editUserForm.role || "gerente"}
              onValueChange={(value: "administrador" | "gerente") => setEditUserForm({...editUserForm, role: value})}
              required
            >
              <SelectTrigger>
                <SelectValue placeholder="Selecione" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="administrador">Administrador</SelectItem>
                <SelectItem value="gerente">Gerente</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Password Reset Section */}
        <Separator className="my-4" />

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Key className="h-4 w-4" />
              <span className="text-sm font-medium">Redefinir Senha</span>
            </div>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => setShowPasswordReset(!showPasswordReset)}
            >
              {showPasswordReset ? "Cancelar" : "Redefinir Senha"}
            </Button>
          </div>

          {showPasswordReset && (
            <div className="space-y-4 p-4 border rounded-lg bg-muted/20">
              <div className="space-y-2">
                <label className="text-sm font-medium">Nova Senha</label>
                <div className="relative">
                  <Input
                    type={showPassword ? "text" : "password"}
                    placeholder="Digite a nova senha"
                    value={passwordData.newPassword}
                    onChange={(e) => setPasswordData({...passwordData, newPassword: e.target.value})}
                    className="pr-10"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
                  >
                    {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                  </button>
                </div>
                {/* Password Strength Indicator */}
                {passwordData.newPassword && (
                  <PasswordStrengthIndicator
                    password={passwordData.newPassword}
                    showFeedback={true}
                    language="pt"
                  />
                )}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Confirmar Nova Senha</label>
                <div className="relative">
                  <Input
                    type={showConfirmPassword ? "text" : "password"}
                    placeholder="Confirme a nova senha"
                    value={passwordData.confirmPassword}
                    onChange={(e) => setPasswordData({...passwordData, confirmPassword: e.target.value})}
                    className="pr-10"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
                  >
                    {showConfirmPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                  </button>
                </div>
              </div>

              <div className="flex justify-end gap-2">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setShowPasswordReset(false);
                    setPasswordData({ newPassword: "", confirmPassword: "" });
                  }}
                >
                  Cancelar
                </Button>
                <Button
                  type="button"
                  size="sm"
                  className="bg-orange-600 hover:bg-orange-700"
                  disabled={resetPasswordMutation.isPending}
                  onClick={handlePasswordReset}
                >
                  {resetPasswordMutation.isPending ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Redefinindo...
                    </>
                  ) : (
                    'Redefinir Senha'
                  )}
                </Button>
              </div>
            </div>
          )}
        </div>

      </div>
      
      <DialogFooter>
        <Button 
          variant="outline" 
          onClick={onClose} 
          type="button"
        >
          Cancelar
        </Button>
        <Button 
          className="bg-javiagens-red hover:bg-javiagens-dark-red"
          type="submit"
          disabled={updateUserMutation.isPending}
        >
          {updateUserMutation.isPending ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Atualizando...
            </>
          ) : (
            'Salvar'
          )}
        </Button>
      </DialogFooter>
    </form>
  );
};
