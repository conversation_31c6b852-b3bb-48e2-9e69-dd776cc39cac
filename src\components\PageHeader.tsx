
/**
 * Page Header Component
 *
 * A reusable header component used across all main pages in the application.
 * Provides consistent styling and layout for page titles and action buttons.
 *
 * Features:
 * - Consistent typography and spacing
 * - Optional action button with icon support
 * - Responsive design
 * - Standardized styling across all pages
 */

import { But<PERSON> } from "@/components/ui/button";
import { ReactNode } from "react";

interface PageHeaderProps {
  title: string;
  subtitle?: string;
  action?: {
    label: string;
    icon?: ReactNode;
    onClick: () => void;
  };
}

export function PageHeader({ title, subtitle, action }: PageHeaderProps) {
  return (
    <div className="flex items-center justify-between mb-6">
      <div>
        <h1 className="text-2xl font-bold tracking-tight">{title}</h1>
        {subtitle && (
          <p className="text-sm text-muted-foreground mt-1">{subtitle}</p>
        )}
      </div>
      {action && (
        <Button onClick={action.onClick} className="bg-javiagens-red hover:bg-javiagens-dark-red">
          {action.icon && <span className="mr-2">{action.icon}</span>}
          {action.label}
        </Button>
      )}
    </div>
  );
}
