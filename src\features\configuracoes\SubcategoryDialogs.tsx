
import { useState, useEffect } from "react";
import { Category } from "./CategoryTable";
import { Subcategory } from "./SubcategoryTable";
import { toastUtils } from "@/lib/toast-utils";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { DatabaseAPI } from "@/api/database";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle
} from "@/components/ui/alert-dialog";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel
} from "@/components/ui/form";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";

import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

const subcategorySchema = z.object({
  name: z.string().min(1, "Nome da subcategoria é obrigatório"),
  category_id: z.string({ required_error: "Categoria é obrigatória" })
});

export function SubcategoryDialogs({
  refreshSubcategories,
  categories,
  isNewSubcategoryOpen,
  setIsNewSubcategoryOpen,
  isEditSubcategoryOpen,
  setIsEditSubcategoryOpen,
  isDeleteSubcategoryOpen,
  setIsDeleteSubcategoryOpen,
  selectedSubcategory,
}) {
  const [isLoading, setIsLoading] = useState(false);

  // Form for creating a new subcategory
  const subcategoryForm = useForm<z.infer<typeof subcategorySchema>>({
    resolver: zodResolver(subcategorySchema),
    defaultValues: {
      name: "",
      category_id: ""
    }
  });
  
  // Form for editing a subcategory
  const editSubcategoryForm = useForm<z.infer<typeof subcategorySchema>>({
    resolver: zodResolver(subcategorySchema),
    defaultValues: {
      name: "",
      category_id: ""
    }
  });

  // Atualiza os valores do formulário quando o selectedSubcategory mudar
  useEffect(() => {
    if (selectedSubcategory && isEditSubcategoryOpen) {
      editSubcategoryForm.reset({
        name: selectedSubcategory.name,
        category_id: selectedSubcategory.category_id || ""
      });
    }
  }, [selectedSubcategory, isEditSubcategoryOpen]);

  // Criar uma nova subcategoria
  const createSubcategory = async (values: z.infer<typeof subcategorySchema>) => {
    try {
      setIsLoading(true);

      // Usar a API do MySQL para criar a subcategoria
      await DatabaseAPI.subcategories.create({
        name: values.name,
        category_id: values.category_id
      });

      toastUtils.success("Sucesso", "Subcategoria criada com sucesso!");

      refreshSubcategories();
      setIsNewSubcategoryOpen(false);
      subcategoryForm.reset();
    } catch (error: any) {
      console.error('Erro ao criar subcategoria:', error);
      toastUtils.error("Erro", error.message || "Não foi possível criar a subcategoria.");
    } finally {
      setIsLoading(false);
    }
  };
  
  // Atualizar uma subcategoria
  const updateSubcategory = async (values: z.infer<typeof subcategorySchema>) => {
    if (!selectedSubcategory) return;

    try {
      setIsLoading(true);

      // Usar a API do MySQL para atualizar a subcategoria
      await DatabaseAPI.subcategories.update(selectedSubcategory.id, {
        name: values.name,
        category_id: values.category_id
      });
      
      toastUtils.success("Sucesso", "Subcategoria atualizada com sucesso!");
      
      refreshSubcategories();
      setIsEditSubcategoryOpen(false);
    } catch (error) {
      console.error('Erro ao atualizar subcategoria:', error);
      toastUtils.error("Erro", "Não foi possível atualizar a subcategoria.");
    } finally {
      setIsLoading(false);
    }
  };
  
  // Excluir uma subcategoria
  const deleteSubcategory = async () => {
    if (!selectedSubcategory) return;

    try {
      setIsLoading(true);

      // Usar a API do MySQL para excluir a subcategoria
      await DatabaseAPI.subcategories.delete(selectedSubcategory.id);

      toastUtils.success("Sucesso", "Subcategoria excluída com sucesso!");

      refreshSubcategories();
      setIsDeleteSubcategoryOpen(false);
    } catch (error: any) {
      console.error('Erro ao excluir subcategoria:', error);
      toastUtils.error("Erro", error.message || "Não foi possível excluir a subcategoria.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      {/* Modal de Nova Subcategoria */}
      <Dialog open={isNewSubcategoryOpen} onOpenChange={setIsNewSubcategoryOpen}>
        <DialogContent className="sm:max-w-[400px]">
          <DialogHeader>
            <DialogTitle>Nova Subcategoria</DialogTitle>
            <DialogDescription>
              Adicione uma nova subcategoria ao sistema.
            </DialogDescription>
          </DialogHeader>
          
          <Form {...subcategoryForm}>
            <form onSubmit={subcategoryForm.handleSubmit(createSubcategory)} className="space-y-4">
              <FormField
                control={subcategoryForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nome da Subcategoria</FormLabel>
                    <FormControl>
                      <Input placeholder="Digite o nome da subcategoria" {...field} />
                    </FormControl>
                  </FormItem>
                )}
              />
              
              <FormField
                control={subcategoryForm.control}
                name="category_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Categoria</FormLabel>
                    <Select 
                      onValueChange={field.onChange} 
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione uma categoria" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {categories.map((category) => (
                          <SelectItem 
                            key={category.id} 
                            value={category.id}
                          >
                            {category.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              />
              
              <DialogFooter>
                <Button 
                  variant="outline" 
                  onClick={() => setIsNewSubcategoryOpen(false)}
                  type="button"
                >
                  Cancelar
                </Button>
                <Button
                  className="bg-javiagens-red hover:bg-javiagens-dark-red"
                  type="submit"
                  disabled={isLoading}
                >
                  {isLoading ? "Salvando..." : "Salvar"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
      
      {/* Modal de Editar Subcategoria */}
      <Dialog open={isEditSubcategoryOpen} onOpenChange={setIsEditSubcategoryOpen}>
        <DialogContent className="sm:max-w-[400px]">
          <DialogHeader>
            <DialogTitle>Editar Subcategoria</DialogTitle>
            <DialogDescription>
              Edite os dados da subcategoria.
            </DialogDescription>
          </DialogHeader>
          
          <Form {...editSubcategoryForm}>
            <form onSubmit={editSubcategoryForm.handleSubmit(updateSubcategory)} className="space-y-4">
              <FormField
                control={editSubcategoryForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nome da Subcategoria</FormLabel>
                    <FormControl>
                      <Input placeholder="Digite o nome da subcategoria" {...field} />
                    </FormControl>
                  </FormItem>
                )}
              />
              
              <FormField
                control={editSubcategoryForm.control}
                name="category_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Categoria</FormLabel>
                    <Select 
                      onValueChange={field.onChange} 
                      value={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione uma categoria" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {categories.map((category) => (
                          <SelectItem 
                            key={category.id} 
                            value={category.id}
                          >
                            {category.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              />
              
              <DialogFooter>
                <Button 
                  variant="outline" 
                  onClick={() => setIsEditSubcategoryOpen(false)}
                  type="button"
                >
                  Cancelar
                </Button>
                <Button
                  className="bg-javiagens-red hover:bg-javiagens-dark-red"
                  type="submit"
                  disabled={isLoading}
                >
                  {isLoading ? "Salvando..." : "Salvar"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
      
      {/* Confirmação de exclusão de Subcategoria */}
      <AlertDialog open={isDeleteSubcategoryOpen} onOpenChange={setIsDeleteSubcategoryOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Excluir Subcategoria</AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja excluir esta subcategoria? Esta ação não pode ser desfeita.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setIsDeleteSubcategoryOpen(false)}>Cancelar</AlertDialogCancel>
            <AlertDialogAction 
              onClick={deleteSubcategory}
              className="bg-red-600 text-white hover:bg-red-700"
            >
              {isLoading ? "Excluindo..." : "Excluir"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
