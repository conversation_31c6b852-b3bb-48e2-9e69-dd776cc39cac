/**
 * Toast Utilities - Standardized Toast Notification System
 *
 * This module provides a centralized and standardized way to display toast notifications
 * throughout the application. All toasts use consistent styling with colored backgrounds
 * and white text for better visibility and user experience.
 *
 * Features:
 * - Consistent color scheme: Green (success), Red (error), Orange (warning), Blue (info)
 * - White text on colored backgrounds for optimal contrast
 * - Animated progress bars (white, semi-transparent) visible on all backgrounds
 * - Organized by functional areas (auth, user, transaction, etc.)
 * - Standardized duration and positioning
 *
 * Usage:
 * - Import toastUtils and use the appropriate method
 * - Example: toastUtils.success("Title", "Description")
 * - Use specific categories for consistency: toastUtils.auth.loginSuccess()
 */
import { toast } from "sonner";

// Enhanced toast utility functions with standardized styling
export const toastUtils = {
  // Success toasts - Green background with white text
  success: (title: string, description?: string) => {
    toast.success(title, {
      description,
      style: {
        background: '#22c55e',
        color: 'white',
        border: 'none',
      },
    });
  },

  // Error toasts - Red background with white text
  error: (title: string, description?: string) => {
    toast.error(title, {
      description,
      style: {
        background: '#ef4444',
        color: 'white',
        border: 'none',
      },
    });
  },

  // Warning toasts - Orange background with white text
  warning: (title: string, description?: string) => {
    toast.warning(title, {
      description,
      style: {
        background: '#f97316',
        color: 'white',
        border: 'none',
      },
    });
  },

  // Info toasts - Blue background with white text
  info: (title: string, description?: string) => {
    toast.info(title, {
      description,
      style: {
        background: '#3b82f6',
        color: 'white',
        border: 'none',
      },
    });
  },

  // Transaction-specific toasts
  transaction: {
    created: () => toastUtils.success(
      "Transação criada",
      "A transação foi criada com sucesso."
    ),
    
    updated: () => toastUtils.success(
      "Transação atualizada", 
      "A transação foi atualizada com sucesso."
    ),
    
    deleted: () => toastUtils.success(
      "Transação excluída",
      "A transação foi excluída com sucesso."
    ),
    
    createError: (error: string) => toastUtils.error(
      "Erro ao criar transação",
      error
    ),
    
    updateError: (error: string) => toastUtils.error(
      "Erro ao atualizar transação", 
      error
    ),
    
    deleteError: (error: string) => toastUtils.error(
      "Erro ao excluir transação",
      error
    ),
  },

  // File upload toasts
  upload: {
    success: (filename?: string) => toastUtils.success(
      "Upload concluído",
      filename ? `O arquivo ${filename} foi carregado com sucesso.` : "O comprovativo foi carregado com sucesso."
    ),
    
    error: (error: string) => toastUtils.error(
      "Erro no upload",
      error
    ),
    
    progress: (filename: string) => toastUtils.info(
      "Upload em andamento",
      `Carregando ${filename}...`
    ),
  },

  // User management toasts
  user: {
    created: () => toastUtils.success(
      "Usuário criado",
      "O usuário foi criado com sucesso."
    ),
    
    updated: () => toastUtils.success(
      "Usuário atualizado",
      "O usuário foi atualizado com sucesso."
    ),
    
    deleted: () => toastUtils.success(
      "Usuário excluído", 
      "O usuário foi excluído com sucesso."
    ),
    
    passwordChanged: () => toastUtils.success(
      "Senha alterada",
      "A senha foi alterada com sucesso."
    ),
    
    error: (action: string, error: string) => toastUtils.error(
      `Erro ao ${action} usuário`,
      error
    ),
  },

  // Authentication toasts
  auth: {
    loginSuccess: () => toastUtils.success(
      "Login realizado",
      "Bem-vindo ao sistema!"
    ),
    
    loginError: (error: string) => toastUtils.error(
      "Erro no login",
      error
    ),
    
    logoutSuccess: () => toastUtils.success(
      "Logout realizado",
      "Você foi desconectado com sucesso."
    ),
    
    sessionExpired: () => toastUtils.warning(
      "Sessão expirada",
      "Sua sessão expirou. Faça login novamente."
    ),
  },

  // Filter and search toasts
  filter: {
    applied: (count: number) => toastUtils.info(
      "Filtros aplicados",
      `${count} registro${count !== 1 ? 's' : ''} encontrado${count !== 1 ? 's' : ''}.`
    ),
    
    cleared: () => toastUtils.info(
      "Filtros limpos",
      "Todos os filtros foram removidos."
    ),
    
    noResults: () => toastUtils.warning(
      "Nenhum resultado",
      "Nenhum registro foi encontrado com os filtros aplicados."
    ),
  },

  // Generic CRUD operations
  crud: {
    created: (entity: string) => toastUtils.success(
      `${entity} criado`,
      `O ${entity.toLowerCase()} foi criado com sucesso.`
    ),
    
    updated: (entity: string) => toastUtils.success(
      `${entity} atualizado`,
      `O ${entity.toLowerCase()} foi atualizado com sucesso.`
    ),
    
    deleted: (entity: string) => toastUtils.success(
      `${entity} excluído`,
      `O ${entity.toLowerCase()} foi excluído com sucesso.`
    ),
    
    error: (action: string, entity: string, error: string) => toastUtils.error(
      `Erro ao ${action} ${entity.toLowerCase()}`,
      error
    ),
  },

  // System notifications
  system: {
    saved: () => toastUtils.success(
      "Configurações salvas",
      "As configurações foram salvas com sucesso."
    ),
    
    exported: (filename: string) => toastUtils.success(
      "Exportação concluída",
      `O arquivo ${filename} foi gerado com sucesso.`
    ),
    
    imported: (count: number) => toastUtils.success(
      "Importação concluída",
      `${count} registro${count !== 1 ? 's' : ''} importado${count !== 1 ? 's' : ''} com sucesso.`
    ),
    
    maintenance: () => toastUtils.warning(
      "Manutenção programada",
      "O sistema entrará em manutenção em breve."
    ),
  },
};

// Convenience function for quick toasts
export const showToast = {
  success: toastUtils.success,
  error: toastUtils.error,
  warning: toastUtils.warning,
  info: toastUtils.info,
};
