/**
 * Progress Bar Component
 *
 * A reusable progress bar component built on Radix UI primitives.
 * Used throughout the application for:
 * - Password strength indicators
 * - Loading states
 * - File upload progress
 * - Any other progress visualization needs
 *
 * Note: This is separate from toast progress bars, which are handled by <PERSON><PERSON>
 * and styled in index.css for visibility on colored backgrounds.
 */
import * as React from "react"
import * as ProgressPrimitive from "@radix-ui/react-progress"

import { cn } from "@/lib/utils"

/**
 * Progress component with customizable styling
 * @param value - Progress value from 0 to 100
 * @param className - Additional CSS classes for customization
 */
const Progress = React.forwardRef<
  React.ElementRef<typeof ProgressPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>
>(({ className, value, ...props }, ref) => (
  <ProgressPrimitive.Root
    ref={ref}
    className={cn(
      "relative h-4 w-full overflow-hidden rounded-full bg-secondary",
      className
    )}
    {...props}
  >
    <ProgressPrimitive.Indicator
      className="h-full w-full flex-1 bg-primary transition-all"
      style={{ transform: `translateX(-${100 - (value || 0)}%)` }}
    />
  </ProgressPrimitive.Root>
))
Progress.displayName = ProgressPrimitive.Root.displayName

export { Progress }
