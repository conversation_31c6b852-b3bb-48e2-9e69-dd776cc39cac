
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { DatabaseAPI } from "@/api/database";
import { AuthAPI } from "@/api/auth";
import { toastUtils } from "@/lib/toast-utils";
import { Transaction } from "@/types/transactions";

export const useTransactionMutations = (callbacks?: {
  onUpdateSuccess?: () => void;
  onUpdateError?: () => void;
}) => {
  const queryClient = useQueryClient();

  // Mutação para criar transação
  const createTransactionMutation = useMutation({
    mutationFn: createTransaction,
    onSuccess: (data) => {
      console.log("Transação criada com sucesso:", data);

      // Invalidar cache de transações e dashboard
      queryClient.invalidateQueries({ queryKey: ["transactions"] });
      queryClient.invalidateQueries({ queryKey: ["dashboard-transactions"] });

      toastUtils.transaction.created();
    },
    onError: (error) => {
      console.error("Erro ao criar transação:", error);

      toastUtils.transaction.createError(error.message);
    },
  });

  // Update transaction mutation
  const updateTransactionMutation = useMutation({
    mutationFn: updateTransaction,
    onSuccess: (data) => {
      console.log("Transaction updated successfully:", data);

      // Invalidate both transactions and dashboard-transactions
      queryClient.invalidateQueries({ queryKey: ["transactions"] });
      queryClient.invalidateQueries({ queryKey: ["dashboard-transactions"] });

      toastUtils.transaction.updated();

      // Chamar callback de sucesso se fornecido
      if (callbacks?.onUpdateSuccess) {
        callbacks.onUpdateSuccess();
      }
    },
    onError: (error) => {
      console.error("Error updating transaction:", error);

      toastUtils.transaction.updateError(error.message);

      // Chamar callback de erro se fornecido
      if (callbacks?.onUpdateError) {
        callbacks.onUpdateError();
      }
    },
  });

  // Mutação para excluir transação
  const deleteTransactionMutation = useMutation({
    mutationFn: deleteTransaction,
    onSuccess: (id) => {
      console.log("Transação excluída com sucesso, ID:", id);

      // Invalidar cache de transações e dashboard
      queryClient.invalidateQueries({ queryKey: ["transactions"] });
      queryClient.invalidateQueries({ queryKey: ["dashboard-transactions"] });

      toastUtils.transaction.deleted();
    },
    onError: (error) => {
      console.error("Erro ao excluir transação:", error);

      toastUtils.transaction.deleteError(error.message);
    },
  });

  // Mutação para remover arquivo de transação
  const removeFileMutation = useMutation({
    mutationFn: removeTransactionFile,
    onSuccess: () => {
      console.log("Arquivo removido com sucesso");

      // Invalidar cache de transações e dashboard
      queryClient.invalidateQueries({ queryKey: ["transactions"] });
      queryClient.invalidateQueries({ queryKey: ["dashboard-transactions"] });

      toastUtils.success("Arquivo removido com sucesso");
    },
    onError: (error) => {
      console.error("Erro ao remover arquivo:", error);

      toastUtils.error("Erro ao remover arquivo: " + error.message);
    },
  });

  return {
    createTransaction: createTransactionMutation.mutate,
    updateTransaction: updateTransactionMutation.mutate,
    deleteTransaction: deleteTransactionMutation.mutate,
    removeFile: removeFileMutation.mutate,
  };
};

// Função para criar uma transação
async function createTransaction(transaction: Omit<Transaction, "id">) {
  console.log("Criando transação:", transaction);

  // Obter sessão do usuário atual
  const session = await AuthAPI.getSession();
  if (!session.user) {
    throw new Error("Usuário não autenticado");
  }

  const userId = session.user.id;
  console.log("Criando transação para o usuário ID:", userId);

  // Preparar dados da transação (sem id, created_at, updated_at, created_by)
  const transactionData: any = {
    description: transaction.description,
    amount: transaction.amount,
    type: transaction.type,
    date: transaction.date,
    user_id: userId
  };

  // Adicionar campos opcionais apenas se tiverem valores válidos
  if (transaction.category_id) {
    transactionData.category_id = transaction.category_id;
  }
  if (transaction.subcategory_id) {
    transactionData.subcategory_id = transaction.subcategory_id;
  }
  if (transaction.comprovativo_url) {
    transactionData.comprovativo_url = transaction.comprovativo_url;
  }

  try {
    // Usar a API do MySQL para criar a transação
    const data = await DatabaseAPI.transactions.create(transactionData);
    return data;
  } catch (error: any) {
    console.error("Erro ao criar transação:", error);
    throw new Error(error.message || "Erro ao criar transação");
  }
}

// Função para atualizar uma transação
async function updateTransaction(transaction: Transaction) {
  console.log("Atualizando transação:", transaction);

  // Verificar se o usuário está autenticado
  const session = await AuthAPI.getSession();
  if (!session.user) {
    throw new Error("Usuário não autenticado");
  }

  // Transformar os dados para o formato esperado pelo backend
  const transactionData: any = {
    amount: transaction.amount,
    type: transaction.type,
    description: transaction.description,
    date: transaction.date,
  };

  // Adicionar campos opcionais apenas se tiverem valores válidos
  if (transaction.category_id && transaction.category_id !== "") {
    transactionData.category_id = transaction.category_id;
  }
  if (transaction.subcategory_id && transaction.subcategory_id !== "") {
    transactionData.subcategory_id = transaction.subcategory_id;
  }
  if (transaction.comprovativo_url && transaction.comprovativo_url !== "") {
    transactionData.comprovativo_url = transaction.comprovativo_url;
  }

  // Log detalhado dos dados que serão enviados
  console.log("Dados da transação para envio:", JSON.stringify(transactionData, null, 2));
  console.log("ID da transação:", transaction.id);

  try {
    // Usar a API do MySQL para atualizar a transação
    const data = await DatabaseAPI.transactions.update(transaction.id, transactionData);
    console.log("Transação atualizada com sucesso:", data);
    return data;
  } catch (error: any) {
    console.error("Erro ao atualizar transação:", error);
    console.error("Dados que causaram erro:", JSON.stringify(transactionData, null, 2));
    throw new Error(error.message || "Erro ao atualizar transação");
  }
}

// Função para excluir uma transação
async function deleteTransaction(id: string) {
  console.log("Excluindo transação com ID:", id);

  try {
    // Usar a API do MySQL para excluir a transação
    await DatabaseAPI.transactions.delete(id);
    return id;
  } catch (error: any) {
    console.error("Erro ao excluir transação:", error);
    throw new Error(error.message || "Erro ao excluir transação");
  }
}

// Função para remover arquivo de uma transação
async function removeTransactionFile(id: string) {
  console.log("Removendo arquivo da transação com ID:", id);

  try {
    // Usar a API do MySQL para remover o arquivo da transação
    await DatabaseAPI.transactions.removeFile(id);
    return id;
  } catch (error: any) {
    console.error("Erro ao remover arquivo da transação:", error);
    throw new Error(error.message || "Erro ao remover arquivo da transação");
  }
}
