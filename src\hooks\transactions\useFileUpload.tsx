
import { useState } from "react";
import { toastUtils } from "@/lib/toast-utils";
import { FileAPI } from "@/api/database";

export const useFileUpload = () => {
  const [isUploading, setIsUploading] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  // Função para preparar arquivo para upload (não faz upload imediato)
  const prepareFileForUpload = (file: File) => {
    console.log('Arquivo selecionado para upload:', file.name);
    setSelectedFile(file);

    // Mostrar apenas uma notificação de arquivo selecionado
    toastUtils.success(
      "Arquivo selecionado",
      `${file.name} está pronto para upload. Clique em "Salvar" para confirmar.`
    );

    // Retorna uma URL temporária para preview
    return URL.createObjectURL(file);
  };

  // Função para fazer upload real do arquivo (chamada apenas no Save)
  const uploadComprovativo = async (file?: File) => {
    const fileToUpload = file || selectedFile;
    if (!fileToUpload) {
      throw new Error("Nenhum arquivo selecionado para upload");
    }

    setIsUploading(true);
    try {
      console.log('Iniciando upload do arquivo:', fileToUpload.name);

      // Usar a API real de upload
      const result = await FileAPI.upload(fileToUpload);

      console.log('Upload concluído:', result);

      // Limpar arquivo selecionado após upload bem-sucedido
      setSelectedFile(null);

      // Retorna a URL do arquivo
      return result.url;
    } catch (error: any) {
      console.error('Erro no upload:', error);
      toastUtils.upload.error(error.message);
      throw error;
    } finally {
      setIsUploading(false);
    }
  };

  // Função para limpar arquivo selecionado
  const clearSelectedFile = () => {
    setSelectedFile(null);
  };

  return {
    prepareFileForUpload,
    uploadComprovativo,
    clearSelectedFile,
    selectedFile,
    isUploading
  };
};
