{"name": "javiagens-backend", "version": "1.0.0", "description": "Backend API server for javiagens cash flow management system", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "build": "echo 'No build step required for Node.js'", "test": "jest"}, "keywords": ["javiagens", "cash-flow", "api", "mysql", "express"], "author": "javiagens", "license": "MIT", "dependencies": {"express": "^4.18.2", "mysql2": "^3.6.5", "bcrypt": "^5.1.1", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "uuid": "^9.0.1", "dotenv": "^16.3.1", "morgan": "^1.10.0", "compression": "^1.7.4"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}