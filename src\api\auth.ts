// Authentication API client for javiagens

import { httpMethods, API_ENDPOINTS, tokenManager, ApiResponse } from './config';

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface LoginResponse {
  user: {
    id: string;
    email: string;
    name: string;
    role: 'administrador' | 'gerente';
  };
  token: string;
  refreshToken: string;
}

export interface ChangePasswordData {
  currentPassword?: string;
  newPassword: string;
  confirmPassword: string;
}

export interface UserProfile {
  id: string;
  name: string;
  email: string;
  phone?: string;
  role: 'administrador' | 'gerente';
  status: boolean;
  avatar?: string;
  last_login?: string;
  created_at: string;
}

// API de autenticação para o sistema javiagens
// Gerencia login, logout, sessões e tokens JWT
export class AuthAPI {
  /**
   * Realiza login do usuário com email e senha
   */
  static async login(credentials: LoginCredentials): Promise<LoginResponse> {
    try {
      const response = await httpMethods.post<LoginResponse>(
        API_ENDPOINTS.auth.login,
        credentials
      );

      if (response.success && response.data) {
        // Store token in localStorage
        tokenManager.setToken(response.data.token);

        // Trigger auth state change immediately
        this.triggerAuthStateChange();

        return response.data;
      }

      throw new Error(response.error || 'Falha no login');
    } catch (error) {
      console.error('Erro de login:', error);
      throw error;
    }
  }

  /**
   * Logout current user
      */
  static async logout(): Promise<void> {
    try {
      await httpMethods.post(API_ENDPOINTS.auth.logout);
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Always remove token from localStorage
      tokenManager.removeToken();

      // Trigger auth state change immediately
      this.triggerAuthStateChange();
    }
  }

  /**
   * Get current user session
      */
  static async getSession(): Promise<{ user: UserProfile | null; token: string | null }> {
    try {
      const token = tokenManager.getToken();
      if (!token) {
        return { user: null, token: null };
      }

      // First try to decode the JWT token locally to get user ID
      try {
        const payload = JSON.parse(atob(token.split('.')[1]));
        console.log('JWT payload:', payload);

        // Check if token is expired
        if (payload.exp && payload.exp * 1000 < Date.now()) {
          console.log('Token expired, removing...');
          tokenManager.removeToken();
          return { user: null, token: null };
        }
      } catch (decodeError) {
        console.error('Failed to decode JWT token:', decodeError);
        tokenManager.removeToken();
        return { user: null, token: null };
      }

      const response = await httpMethods.get<{user: UserProfile}>(API_ENDPOINTS.auth.me);

      if (response.success && response.data) {
        console.log('Session user data:', response.data);
        return { user: response.data.user, token };
      }

      // Token is invalid, remove it
      tokenManager.removeToken();
      return { user: null, token: null };
    } catch (error) {
      console.error('Get session error:', error);
      tokenManager.removeToken();
      return { user: null, token: null };
    }
  }

  /**
   * Get current user profile
  userId).single()
   */
  static async getCurrentUser(): Promise<UserProfile | null> {
    try {
      const response = await httpMethods.get<UserProfile>(API_ENDPOINTS.auth.me);
      return response.success ? response.data || null : null;
    } catch (error) {
      console.error('Get current user error:', error);
      return null;
    }
  }

  /**
   * Change user password
      */
  static async changePassword(data: ChangePasswordData): Promise<void> {
    try {
      const response = await httpMethods.post(API_ENDPOINTS.auth.changePassword, data);
      
      if (!response.success) {
        throw new Error(response.error || 'Password change failed');
      }
    } catch (error) {
      console.error('Change password error:', error);
      throw error;
    }
  }

  /**
   * Atualiza o token de autenticação
   */
  static async refreshToken(): Promise<string | null> {
    try {
      const response = await httpMethods.post<{ token: string }>(API_ENDPOINTS.auth.refresh);
      
      if (response.success && response.data) {
        tokenManager.setToken(response.data.token);
        return response.data.token;
      }
      
      return null;
    } catch (error) {
      console.error('Refresh token error:', error);
      tokenManager.removeToken();
      return null;
    }
  }

  /**
   * Verifica se o usuário está autenticado
   */
  static isAuthenticated(): boolean {
    return !!tokenManager.getToken();
  }

  /**
   * Auth state change listener (improved version)
      */
  static onAuthStateChange(callback: (user: UserProfile | null) => void): () => void {
    let intervalId: NodeJS.Timeout;
    let isChecking = false;

    const checkAuth = async () => {
      if (isChecking) return; // Prevent concurrent checks
      isChecking = true;

      try {
        const session = await this.getSession();
        callback(session.user);
      } catch (error) {
        console.error('Auth state check error:', error);
        callback(null);
      } finally {
        isChecking = false;
      }
    };

    // Check immediately
    checkAuth();

    // Check less frequently (every 2 minutes) to reduce server load
    intervalId = setInterval(checkAuth, 2 * 60 * 1000);

    // Listen for storage changes (when token is added/removed in another tab)
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'auth_token') {
        checkAuth();
      }
    };

    // Listen for custom auth state change events
    const handleCustomAuthChange = () => {
      checkAuth();
    };

    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('auth-state-changed', handleCustomAuthChange);

    // Return cleanup function
    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('auth-state-changed', handleCustomAuthChange);
    };
  }

  /**
   * Trigger auth state change manually (for immediate updates)
   */
  static triggerAuthStateChange(): void {
    // Dispatch a custom event to trigger auth state checks
    window.dispatchEvent(new CustomEvent('auth-state-changed'));
  }
}

// Export for backward compatibility with existing code
export const authAPI = AuthAPI;
