# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=viagens
DB_USER=your_db_user
DB_PASSWORD=your_db_password

# Server Configuration
PORT=3001
HOST=0.0.0.0
NODE_ENV=development

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here_change_in_production
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# File Upload Configuration
UPLOAD_DIR=uploads
MAX_FILE_SIZE=5242880
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,application/pdf

# Body Parser Configuration
BODY_LIMIT=10mb

# Security Configuration
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration (múltiplas origens separadas por vírgula)
CORS_ORIGIN=http://localhost:8083,http://************:8083
