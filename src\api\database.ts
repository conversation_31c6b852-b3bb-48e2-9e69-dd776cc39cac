// Cliente da API de banco de dados para o sistema javiagens
// Este arquivo centraliza todas as operações de banco de dados,
// fornecendo uma interface consistente para interagir com o backend MySQL

import { httpMethods, API_ENDPOINTS, API_BASE_URL, PaginatedResponse, ApiResponse, tokenManager } from './config';

// Definições de tipos para as entidades do sistema
// Estas interfaces definem a estrutura dos dados que trafegam entre o frontend e o backend

export interface Profile {
  id: string;
  name: string;
  email: string;
  phone?: string;
  role: 'administrador' | 'gerente';
  status: boolean;
  avatar?: string;
  last_login?: string;
  created_at: string;
}

export interface Category {
  id: string;
  name: string;
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface Subcategory {
  id: string;
  name: string;
  category_id: string;
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface Transaction {
  id: string;
  description: string;
  amount: number;
  type: 'entrada' | 'saida';
  date: string;
  category_id?: string;
  subcategory_id?: string;
  created_by: string;
  created_at: string;
  updated_at: string;
  comprovativo_url?: string;
}

export interface CreateUserData {
  name: string;
  email: string;
  password: string;
  phone?: string;
  role: 'administrador' | 'gerente';
}

export interface UpdateProfileData {
  name?: string;
  phone?: string;
  avatar?: string;
}

// Construtor de consultas para facilitar a criação de queries dinâmicas
// Permite encadear métodos para filtrar, ordenar e limitar resultados
export class QueryBuilder<T> {
  private endpoint: string;
  private filters: Record<string, any> = {};
  private selectFields: string = '*';
  private orderBy: { field: string; ascending: boolean } | null = null;
  private limitValue: number | null = null;

  constructor(endpoint: string) {
    this.endpoint = endpoint;
  }

  select(fields: string = '*'): this {
    this.selectFields = fields;
    return this;
  }

  eq(field: string, value: any): this {
    this.filters[`${field}_eq`] = value;
    return this;
  }

  neq(field: string, value: any): this {
    this.filters[`${field}_neq`] = value;
    return this;
  }

  gt(field: string, value: any): this {
    this.filters[`${field}_gt`] = value;
    return this;
  }

  gte(field: string, value: any): this {
    this.filters[`${field}_gte`] = value;
    return this;
  }

  lt(field: string, value: any): this {
    this.filters[`${field}_lt`] = value;
    return this;
  }

  lte(field: string, value: any): this {
    this.filters[`${field}_lte`] = value;
    return this;
  }

  like(field: string, value: string): this {
    this.filters[`${field}_like`] = value;
    return this;
  }

  in(field: string, values: any[]): this {
    this.filters[`${field}_in`] = values.join(',');
    return this;
  }

  order(field: string, options: { ascending?: boolean } = {}): this {
    this.orderBy = { field, ascending: options.ascending !== false };
    return this;
  }

  limit(count: number): this {
    this.limitValue = count;
    return this;
  }

  async execute(): Promise<T[]> {
    const params: Record<string, any> = {
      ...this.filters,
      select: this.selectFields,
    };

    if (this.orderBy) {
      params.order_by = this.orderBy.field;
      params.order_direction = this.orderBy.ascending ? 'asc' : 'desc';
    }

    if (this.limitValue) {
      params.limit = this.limitValue;
    }

    const response = await httpMethods.get<T[]>(this.endpoint, params);
    return response.data || [];
  }

  async single(): Promise<T | null> {
    this.limit(1);
    const results = await this.execute();
    return results.length > 0 ? results[0] : null;
  }
}

// Classe principal da API de banco de dados
// Organiza todas as operações CRUD por entidade (perfis, categorias, subcategorias, transações, usuários)
export class DatabaseAPI {
  /**
   * Operações relacionadas aos perfis de usuário
   * Inclui busca, atualização e gerenciamento de perfis
   */
  static profiles = {
    select: (fields?: string) => new QueryBuilder<Profile>(API_ENDPOINTS.profiles.list).select(fields),
    
    async update(id: string, data: UpdateProfileData): Promise<Profile> {
      const response = await httpMethods.put<Profile>(`${API_ENDPOINTS.profiles.update}/${id}`, data);
      if (!response.success || !response.data) {
        throw new Error(response.error || 'Update failed');
      }
      return response.data;
    },
  };

  /**
   * Users operations (admin only)
      */
  static users = {
    async list(): Promise<Profile[]> {
      const response = await httpMethods.get<Profile[]>(API_ENDPOINTS.users.list);
      return response.data || [];
    },

    async create(userData: CreateUserData): Promise<Profile> {
      const response = await httpMethods.post<Profile>(API_ENDPOINTS.users.create, userData);
      if (!response.success || !response.data) {
        throw new Error(response.error || 'User creation failed');
      }
      return response.data;
    },

    async update(id: string, data: Partial<Profile>): Promise<Profile> {
      const response = await httpMethods.put<Profile>(`${API_ENDPOINTS.users.update}/${id}`, data);
      if (!response.success || !response.data) {
        throw new Error(response.error || 'User update failed');
      }
      return response.data;
    },

    async delete(id: string): Promise<void> {
      const response = await httpMethods.delete(`${API_ENDPOINTS.users.delete}/${id}`);
      if (!response.success) {
        throw new Error(response.error || 'User deletion failed');
      }
    },

    async deactivate(id: string): Promise<void> {
      const response = await httpMethods.delete(`${API_ENDPOINTS.users.delete}/${id}/deactivate`);
      if (!response.success) {
        throw new Error(response.error || 'User deactivation failed');
      }
    },

    async resetPassword(id: string, newPassword: string): Promise<void> {
      const response = await httpMethods.put(`${API_ENDPOINTS.users.update}/${id}/reset-password`, {
        newPassword
      });
      if (!response.success) {
        throw new Error(response.error || 'Password reset failed');
      }
    },
  };

  /**
   * Categories operations
     */
  static categories = {
    select: (fields?: string) => new QueryBuilder<Category>(API_ENDPOINTS.categories.list).select(fields),
    
    async create(data: { name: string }): Promise<Category> {
      const response = await httpMethods.post<Category>(API_ENDPOINTS.categories.create, data);
      if (!response.success || !response.data) {
        throw new Error(response.error || 'Category creation failed');
      }
      return response.data;
    },

    async update(id: string, data: { name: string }): Promise<Category> {
      const response = await httpMethods.put<Category>(`${API_ENDPOINTS.categories.update}/${id}`, data);
      if (!response.success || !response.data) {
        throw new Error(response.error || 'Category update failed');
      }
      return response.data;
    },

    async delete(id: string): Promise<void> {
      const response = await httpMethods.delete(`${API_ENDPOINTS.categories.delete}/${id}`);
      if (!response.success) {
        throw new Error(response.error || 'Category deletion failed');
      }
    },
  };

  /**
   * Subcategories operations
   */
  static subcategories = {
    select: (fields?: string) => new QueryBuilder<Subcategory>(API_ENDPOINTS.subcategories.list).select(fields),
    
    async create(data: { name: string; category_id: string }): Promise<Subcategory> {
      const response = await httpMethods.post<Subcategory>(API_ENDPOINTS.subcategories.create, data);
      if (!response.success || !response.data) {
        throw new Error(response.error || 'Subcategory creation failed');
      }
      return response.data;
    },

    async update(id: string, data: { name: string; category_id: string }): Promise<Subcategory> {
      const response = await httpMethods.put<Subcategory>(`${API_ENDPOINTS.subcategories.update}/${id}`, data);
      if (!response.success || !response.data) {
        throw new Error(response.error || 'Subcategory update failed');
      }
      return response.data;
    },

    async delete(id: string): Promise<void> {
      const response = await httpMethods.delete(`${API_ENDPOINTS.subcategories.delete}/${id}`);
      if (!response.success) {
        throw new Error(response.error || 'Subcategory deletion failed');
      }
    },
  };

  /**
   * Transactions operations
   */
  static transactions = {
    select: (fields?: string) => new QueryBuilder<Transaction>(API_ENDPOINTS.transactions.list).select(fields),
    
    async create(data: Omit<Transaction, 'id' | 'created_at' | 'updated_at' | 'created_by'>): Promise<Transaction> {
      const response = await httpMethods.post<Transaction>(API_ENDPOINTS.transactions.create, data);
      if (!response.success || !response.data) {
        throw new Error(response.error || 'Transaction creation failed');
      }
      return response.data;
    },

    async update(id: string, data: Partial<Transaction>): Promise<Transaction> {
      console.log(`Enviando PUT para ${API_ENDPOINTS.transactions.update}/${id}`);
      console.log("Dados enviados:", JSON.stringify(data, null, 2));

      const response = await httpMethods.put<Transaction>(`${API_ENDPOINTS.transactions.update}/${id}`, data);

      console.log("Resposta do servidor:", response);

      if (!response.success || !response.data) {
        const errorMessage = response.error || 'Transaction update failed';
        console.error("Erro na atualização da transação:", errorMessage);
        throw new Error(errorMessage);
      }
      return response.data;
    },

    async delete(id: string): Promise<void> {
      const response = await httpMethods.delete(`${API_ENDPOINTS.transactions.delete}/${id}`);
      if (!response.success) {
        throw new Error(response.error || 'Transaction deletion failed');
      }
    },

    async removeFile(id: string): Promise<void> {
      const response = await httpMethods.delete(`${API_ENDPOINTS.transactions.delete}/${id}/file`);
      if (!response.success) {
        throw new Error(response.error || 'File removal failed');
      }
    },
  };
}

// File upload operations
export class FileAPI {
  /**
   * Upload file
   * Replaces: supaase.storage.from('comprovativos').upload()
   */
  static async upload(file: File, path?: string): Promise<{ path: string; url: string }> {
    const formData = new FormData();
    formData.append('file', file);
    if (path) {
      formData.append('path', path);
    }

    const response = await fetch(`${API_BASE_URL}${API_ENDPOINTS.files.upload}`, {
      method: 'POST',
      headers: {
        // Don't set Content-Type for FormData - browser will set it automatically with boundary
        ...tokenManager.getAuthHeaders(),
      },
      body: formData,
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Upload error response:', errorText);
      throw new Error(`Upload failed: ${response.statusText}`);
    }

    const result = await response.json();
    if (!result.success) {
      throw new Error(result.error || 'Upload failed');
    }

    // Fix the URL to point to the backend static files endpoint
    const data = result.data;
    if (data.url && data.url.startsWith('/uploads/')) {
      // Use the static files route directly to avoid iframe issues
      data.url = `${API_BASE_URL.replace('/api', '')}${data.url}`;
    }

    return data;
  }

  /**
   * Delete file
   */
  static async delete(path: string): Promise<void> {
    const response = await httpMethods.delete(`${API_ENDPOINTS.files.delete}?path=${encodeURIComponent(path)}`);
    if (!response.success) {
      throw new Error(response.error || 'File deletion failed');
    }
  }
}

// Export for backward compatibility
export const databaseAPI = DatabaseAPI;
export const fileAPI = FileAPI;
