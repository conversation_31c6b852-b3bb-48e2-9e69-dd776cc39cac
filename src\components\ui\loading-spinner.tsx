// Componente reutilizável para estados de carregamento
// Centraliza a lógica de loading para reduzir duplicação

import React from 'react';
import { cn } from '@/lib/utils';
import { Loader2 } from 'lucide-react';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'default' | 'primary' | 'secondary';
  className?: string;
  text?: string;
  fullScreen?: boolean;
}

const sizeClasses = {
  sm: 'h-4 w-4',
  md: 'h-6 w-6',
  lg: 'h-8 w-8',
  xl: 'h-12 w-12'
};

const variantClasses = {
  default: 'text-muted-foreground',
  primary: 'text-javiagens-red',
  secondary: 'text-secondary-foreground'
};

/**
 * Componente de spinner de carregamento reutilizável
 * Substitui múltiplas implementações de loading espalhadas pelo código
 */
export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  variant = 'default',
  className,
  text,
  fullScreen = false
}) => {
  const spinnerContent = (
    <div className={cn(
      'flex flex-col items-center justify-center gap-2',
      fullScreen && 'min-h-screen',
      className
    )}>
      <Loader2 className={cn(
        'animate-spin',
        sizeClasses[size],
        variantClasses[variant]
      )} />
      {text && (
        <p className={cn(
          'text-sm',
          variantClasses[variant]
        )}>
          {text}
        </p>
      )}
    </div>
  );

  if (fullScreen) {
    return (
      <div className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center">
        {spinnerContent}
      </div>
    );
  }

  return spinnerContent;
};

// Componente específico para carregamento de autenticação
export const AuthLoadingSpinner: React.FC = () => (
  <LoadingSpinner
    size="lg"
    variant="primary"
    text="Carregando..."
    fullScreen
  />
);

// Componente para carregamento inline em botões
interface ButtonLoadingProps {
  isLoading: boolean;
  children: React.ReactNode;
  loadingText?: string;
}

export const ButtonLoading: React.FC<ButtonLoadingProps> = ({
  isLoading,
  children,
  loadingText
}) => {
  if (isLoading) {
    return (
      <div className="flex items-center gap-2">
        <LoadingSpinner size="sm" variant="secondary" />
        <span>{loadingText || 'Carregando...'}</span>
      </div>
    );
  }

  return <>{children}</>;
};
