
import { cn } from "@/lib/utils";
import { CircleCheck, CircleX } from "lucide-react";

interface UserStatusBadgeProps {
  status: boolean;
}

export const UserStatusBadge = ({ status }: UserStatusBadgeProps) => {
  return (
    <div className="flex items-center gap-1">
      {status ? (
        <CircleCheck className="h-5 w-5 text-green-500" />
      ) : (
        <CircleX className="h-5 w-5 text-red-500" />
      )}
      <span
        className={cn(
          status ? "text-green-600" : "text-red-600"
        )}
      >
        {status ? "Ativo" : "Inativo"}
      </span>
    </div>
  );
};
