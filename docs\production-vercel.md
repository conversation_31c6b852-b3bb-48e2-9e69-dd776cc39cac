# Guia de Deploy em Produção (Vercel)

Este guia descreve como publicar o frontend no Vercel e conectar ao backend Express/MySQL, incluindo a configuração correta das variáveis de ambiente.

## Visão Geral
- Frontend: React + Vite → Vercel (estático)
- Backend: Node/Express + MySQL → serviço dedicado (Render, Railway, Fly.io, AlwaysData, EC2, etc.)
- Comunicação segura via HTTPS, com CORS configurado

## 1) Preparar o Backend
1. Configure variáveis de ambiente (exemplo):
   - NODE_ENV=production
   - PORT=3001
   - HOST=0.0.0.0
   - DB_HOST=...
   - DB_PORT=3306
   - DB_NAME=...
   - DB_USER=...
   - DB_PASSWORD=...
   - JWT_SECRET=valor_aleatorio_muito_forte
   - JWT_EXPIRES_IN=24h
   - JWT_REFRESH_EXPIRES_IN=7d
   - BCRYPT_ROUNDS=12
   - CORS_ORIGIN=https://seu-projeto.vercel.app
2. Garanta que o backend esteja acessível via HTTPS público (domínio próprio ou do provedor).
3. Teste o endpoint de saúde: GET https://seu-backend/api/health (ou /health conforme sua publicação).

## 2) Preparar o Frontend (Vercel)
1. Importe o repositório no Vercel e selecione a raiz (projeto Vite).
2. Build Command: `npm run build` | Output Directory: `dist`.
3. Configure variáveis em Settings → Environment Variables:
   - VITE_API_BASE_URL = https://seu-backend/api
   - (Opcional) VITE_NODE_ENV = production
4. Se desejar ambientes distintos, configure para Production/Preview/Development.
5. Deploy e valide.

## 3) Boas Práticas de Segurança
- Nunca cometer `.env` no repositório.
- Usar segredos fortes (JWT_SECRET com ≥ 32 bytes aleatórios).
- Restringir CORS_ORIGIN a domínios exatos de produção.
- Reduzir tempo de expiração de access tokens (ex.: 15m) e usar refresh tokens curtos.
- Rotacionar credenciais periodicamente (DB, JWT, etc.).
- Minimizar logs sensíveis e mascarar PII.

## 4) Diferenças Dev vs. Produção
- HTTPS obrigatório no backend para evitar mixed content com Vercel (HTTPS).
- CORS em produção usa `CORS_ORIGIN`; em desenvolvimento, o backend está permissivo.
- Tokens e limites de taxa podem ser mais restritivos em produção.

## 5) Checklist Rápido
- [ ] Backend online com HTTPS e `/health` OK
- [ ] JWT_SECRET definido e forte
- [ ] CORS_ORIGIN inclui apenas `https://seu-projeto.vercel.app`
- [ ] DB acessível e credenciais corretas
- [ ] VITE_API_BASE_URL no Vercel aponta para `https://seu-backend/api`
- [ ] Teste de login válido no domínio do Vercel

