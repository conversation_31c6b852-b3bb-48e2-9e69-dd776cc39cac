import { useQuery } from "@tanstack/react-query";
import { httpMethods, API_ENDPOINTS } from "@/api/config";
import { Transaction } from "@/types/transactions";

export interface TransactionFilters {
  search?: string;
  type?: string;
  category_id?: string;
  subcategory_id?: string;
  start_date?: string;
  end_date?: string;
  min_amount?: number;
  max_amount?: number;
}

export interface PaginationParams {
  page?: number;
  limit?: number;
}

export interface TransactionResponse {
  transactions: Transaction[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export const useFetchTransactions = (
  filters: TransactionFilters = {},
  pagination: PaginationParams = {}
) => {
  return useQuery({
    queryKey: ["transactions", filters, pagination],
    queryFn: () => fetchTransactions(filters, pagination),
  });
};

// Função para buscar transações da API
export const fetchTransactions = async (
  filters: TransactionFilters = {},
  pagination: PaginationParams = {}
): Promise<TransactionResponse> => {
  try {
    // Construir parâmetros da query
    const queryParams = new URLSearchParams();

    // Parâmetros de paginação
    if (pagination.page) queryParams.append('page', pagination.page.toString());
    if (pagination.limit) queryParams.append('limit', pagination.limit.toString());

    // Parâmetros de filtro
    if (filters.search) queryParams.append('search', filters.search);
    if (filters.type) queryParams.append('type', filters.type);
    if (filters.category_id) queryParams.append('category_id', filters.category_id);
    if (filters.subcategory_id) queryParams.append('subcategory_id', filters.subcategory_id);
    if (filters.start_date) queryParams.append('start_date', filters.start_date);
    if (filters.end_date) queryParams.append('end_date', filters.end_date);
    if (filters.min_amount) queryParams.append('min_amount', filters.min_amount.toString());
    if (filters.max_amount) queryParams.append('max_amount', filters.max_amount.toString());

    const url = `${API_ENDPOINTS.transactions.list}?${queryParams.toString()}`;

    // Usar a API HTTP para buscar transações com JOINs
    const response = await httpMethods.get(url) as {
      success: boolean;
      data: any[];
      pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
      };
    };

    const data: any[] = response.data || [];
    const paginationData = response.pagination || { page: 1, limit: 20, total: 0, totalPages: 0 };

    console.log("Transactions: Transações buscadas:", data?.length || 0);
    console.log("Transactions: Paginação:", paginationData);
    if (data && data.length > 0) {
      console.log("Transactions: Primeira transação exemplo:", JSON.stringify(data[0]));
    }

    // Mapear os dados para o formato esperado pelo frontend
    const transactions = data.map((item: any) => ({
      ...item,
      amount: parseFloat(item.amount) || 0, // Converter string para number
      type: (item.type === "entrada" || item.type === "saida") ? item.type as "entrada" | "saida" : "entrada",
      categoria: item.category_name || "",
      subcategoria: item.subcategory_name || "",
    })) as Transaction[];

    return {
      transactions,
      pagination: paginationData
    };
  } catch (error: any) {
    console.error("Transactions: Erro ao buscar transações:", error);
    throw new Error(error.message || "Erro ao buscar transações");
  }
};
