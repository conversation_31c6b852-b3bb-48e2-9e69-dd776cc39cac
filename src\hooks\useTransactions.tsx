
import { Transaction } from "@/types/transactions";
import { useFetchTransactions, TransactionFilters, PaginationParams } from "./transactions/useFetchTransactions";
import { useTransactionMutations } from "./transactions/useTransactionMutations";
import { useFileUpload } from "./transactions/useFileUpload";

export type { Transaction };

export const useTransactions = (
  filters: TransactionFilters = {},
  pagination: PaginationParams = {},
  callbacks?: {
    onUpdateSuccess?: () => void;
    onUpdateError?: () => void;
  }
) => {
  const { data, isLoading, error } = useFetchTransactions(filters, pagination);
  const { createTransaction, updateTransaction, deleteTransaction, removeFile } = useTransactionMutations(callbacks);
  const { prepareFileForUpload, uploadComprovativo, clearSelectedFile, selectedFile, isUploading } = useFileUpload();

  const transactions = data?.transactions || [];
  const paginationData = data?.pagination || { page: 1, limit: 20, total: 0, totalPages: 0 };

  console.log("useTransactions: Got transactions:", transactions?.length || 0);
  console.log("useTransactions: Pagination data:", paginationData);
  if (error) {
    console.error("useTransactions: Error fetching transactions:", error);
  }

  return {
    transactions,
    pagination: paginationData,
    isLoading,
    error,
    isUploading,
    createTransaction,
    updateTransaction,
    deleteTransaction,
    removeFile,
    prepareFileForUpload,
    uploadComprovativo,
    clearSelectedFile,
    selectedFile,
  };
};
