
import { useLocation, Link } from "react-router-dom";
import { useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Home } from "lucide-react";

const NotFound = () => {
  const location = useLocation();

  useEffect(() => {
    console.error(
      "404 Error: Usuário tentou acessar rota inexistente:",
      location.pathname
    );
  }, [location.pathname]);

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50 p-4">
      <div className="text-center space-y-4 max-w-md mx-auto">
        <h1 className="text-6xl font-bold text-javiagens-red">404</h1>
        <p className="text-xl text-gray-600 mb-6">
          Página não encontrada
        </p>
        <p className="text-gray-500 mb-8">
          A página que você está procurando pode ter sido removida ou está temporariamente indisponível.
        </p>
        <Button asChild className="bg-javiagens-red hover:bg-javiagens-dark-red">
          <Link to="/" className="flex items-center gap-2">
            <Home size={16} />
            Voltar ao Dashboard
          </Link>
        </Button>
      </div>
    </div>
  );
};

export default NotFound;
