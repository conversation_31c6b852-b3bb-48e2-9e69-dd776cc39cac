
import { AlertCircle } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface PermissionAlertProps {
  action: "criar" | "editar" | "excluir";
}

export const PermissionAlert = ({ action }: PermissionAlertProps) => {
  return (
    <Alert variant="destructive" className="bg-red-600 text-white border-red-600 mb-4">
      <AlertCircle className="h-4 w-4" />
      <AlertDescription>
        Apenas administradores têm permissão para {action} usuários.
      </AlertDescription>
    </Alert>
  );
};
