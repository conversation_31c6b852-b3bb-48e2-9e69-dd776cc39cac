
// Cliente de banco de dados MySQL para javiagens
import { mysqlClient as mysqlClientInstance } from '@/api/client';
import { DatabaseAPI } from '@/api/database';

// Exportar o cliente MySQL diretamente
export const mysqlClient = mysqlClientInstance;

// Function to delete a user (replaces Edge Function)
export async function deleteUserById(userId: string) {
  try {
    // Use MySQL API to delete user from both users and profiles tables
    await DatabaseAPI.users.delete(userId);

    // Success - user was completely deleted
    return {
      data: { user: null },
      error: null
    };

  } catch (error: any) {
    console.error("Error deleting user:", error);
    return {
      data: { user: null },
      error: {
        message: error.message || "Unknown error deleting user",
        name: "AuthApiError",
        status: 500,
        code: "unknown_error" as const
      }
    };
  }
}
