
// Cliente de compatibilidade MySQL para manter interface similar ao Supabase
// Este arquivo fornece uma camada de compatibilidade para facilitar a migração

import { AuthAPI, LoginCredentials, UserProfile } from './auth';
import { DatabaseAPI, Profile, Category, Subcategory, Transaction } from './database';
import { FileAPI } from './database';

// Cliente de autenticação - gerencia login, logout e sessões
class AuthClient {
  // Realiza login com email e senha
  async signInWithPassword(credentials: LoginCredentials) {
    try {
      const result = await AuthAPI.login(credentials);
      return {
        data: {
          user: result.user,
          session: {
            access_token: result.token,
            user: result.user,
          },
        },
        error: null,
      };
    } catch (error: any) {
      return {
        data: { user: null, session: null },
        error: { message: error.message || 'Falha no login' },
      };
    }
  }

  // Realiza logout do usuário
  async signOut() {
    try {
      await AuthAPI.logout();
      return { error: null };
    } catch (error: any) {
      return { error: { message: error.message || 'Falha no logout' } };
    }
  }

  async getSession() {
    try {
      const session = await AuthAPI.getSession();
      return {
        data: {
          session: session.user ? {
            access_token: session.token,
            user: session.user,
          } : null,
        },
        error: null,
      };
    } catch (error: any) {
      return {
        data: { session: null },
        error: { message: error.message || 'Session check failed' },
      };
    }
  }

  async updateUser(data: { password?: string }) {
    try {
      if (data.password) {
        await AuthAPI.changePassword({
          newPassword: data.password,
          confirmPassword: data.password,
        });
      }
      return { data: { user: await AuthAPI.getCurrentUser() }, error: null };
    } catch (error: any) {
      return {
        data: { user: null },
        error: { message: error.message || 'Update failed' },
      };
    }
  }

  onAuthStateChange(callback: (event: string, session: any) => void) {
    // Listen for custom auth state change events
    const handleAuthChange = async () => {
      const sessionData = await this.getSession();
      const session = sessionData.data.session;
      callback(session ? 'SIGNED_IN' : 'SIGNED_OUT', session);
    };

    // Listen for custom auth state change events
    window.addEventListener('auth-state-changed', handleAuthChange);

    // Also use the original polling mechanism as fallback
    const cleanup = AuthAPI.onAuthStateChange((user) => {
      const session = user ? { access_token: 'token', user } : null;
      callback(user ? 'SIGNED_IN' : 'SIGNED_OUT', session);
    });

    // Check immediately
    handleAuthChange();

    // Return cleanup function
    return () => {
      window.removeEventListener('auth-state-changed', handleAuthChange);
      cleanup();
    };
  }
}


// Cliente de tabela - fornece interface similar ao Supabase para consultas
class TableClient<T> {
  constructor(private tableName: string) {}

  // Seleciona campos específicos da tabela
  select(fields?: string) {
    switch (this.tableName) {
      case 'profiles':
        return DatabaseAPI.profiles.select(fields) as any;
      case 'categories':
        return DatabaseAPI.categories.select(fields) as any;
      case 'subcategories':
        return DatabaseAPI.subcategories.select(fields) as any;
      case 'transactions':
        return DatabaseAPI.transactions.select(fields) as any;
      default:
        throw new Error(`Table ${this.tableName} not supported`);
    }
  }

  insert(data: Partial<T>) {
    // Return an object that supports chaining with .select()
    return {
      select: (_fields?: string) => ({
        async execute() {
          try {
            let result: any;
            switch (this.tableName) {
              case 'profiles':
                // For profiles, we should use the users.create method for new users
                result = await DatabaseAPI.users.create(data as any);
                break;
              case 'categories':
                result = await DatabaseAPI.categories.create(data as any);
                break;
              case 'subcategories':
                result = await DatabaseAPI.subcategories.create(data as any);
                break;
              case 'transactions':
                result = await DatabaseAPI.transactions.create(data as any);
                break;
              default:
                throw new Error(`Insert not supported for table ${this.tableName}`);
            }
            return { data: result, error: null };
          } catch (error: any) {
            return { data: null, error: { message: error.message } };
          }
        }
      }),

      // For backward compatibility, also support direct execution
      async execute() {
        try {
          let result: any;
          switch (this.tableName) {
            case 'profiles':
              result = await DatabaseAPI.users.create(data as any);
              break;
            case 'categories':
              result = await DatabaseAPI.categories.create(data as any);
              break;
            case 'subcategories':
              result = await DatabaseAPI.subcategories.create(data as any);
              break;
            case 'transactions':
              result = await DatabaseAPI.transactions.create(data as any);
              break;
            default:
              throw new Error(`Insert not supported for table ${this.tableName}`);
          }
          return { data: result, error: null };
        } catch (error: any) {
          return { data: null, error: { message: error.message } };
        }
      }
    };
  }

  async update(data: Partial<T>) {
    // This would need to be called with .eq() first to specify the ID
    // For now, return a builder that can handle the update
    return {
      eq: (_field: string, value: any) => ({
        async execute() {
          try {
            let result: any;
            switch (this.tableName) {
              case 'profiles':
                result = await DatabaseAPI.profiles.update(value, data as any);
                break;
              case 'categories':
                result = await DatabaseAPI.categories.update(value, data as any);
                break;
              case 'subcategories':
                result = await DatabaseAPI.subcategories.update(value, data as any);
                break;
              case 'transactions':
                result = await DatabaseAPI.transactions.update(value, data as any);
                break;
              default:
                throw new Error(`Update not supported for table ${this.tableName}`);
            }
            return { data: result, error: null };
          } catch (error: any) {
            return { data: null, error: { message: error.message } };
          }
        },
      }),
    };
  }

  async delete() {
    return {
      eq: (_field: string, value: any) => ({
        async execute() {
          try {
            switch (this.tableName) {
              case 'categories':
                await DatabaseAPI.categories.delete(value);
                break;
              case 'subcategories':
                await DatabaseAPI.subcategories.delete(value);
                break;
              case 'transactions':
                await DatabaseAPI.transactions.delete(value);
                break;
              default:
                throw new Error(`Delete not supported for table ${this.tableName}`);
            }
            return { data: null, error: null };
          } catch (error: any) {
            return { data: null, error: { message: error.message } };
          }
        },
      }),
    };
  }
}

class StorageClient {
  from(_bucket: string) {
    return {
      async upload(path: string, file: File) {
        try {
          const result = await FileAPI.upload(file, path);
          return { data: { path: result.path }, error: null };
        } catch (error: any) {
          return { data: null, error: { message: error.message } };
        }
      },

      async remove(paths: string[]) {
        try {
          await Promise.all(paths.map(path => FileAPI.delete(path)));
          return { data: null, error: null };
        } catch (error: any) {
          return { data: null, error: { message: error.message } };
        }
      },

      getPublicUrl(path: string) {
        // Return the public URL for the file
        // In a real implementation, this would be the URL to access the file
        const baseUrl = window.location.origin;
        return {
          data: {
            publicUrl: `${baseUrl}/api/files/view/${encodeURIComponent(path)}`,
          },
        };
      },
    };
  }
}

class FunctionsClient {
  async invoke(functionName: string, options: { body: any }) {
    try {
      switch (functionName) {
        case 'create-user':
          // Use the DatabaseAPI to create a new user
          const userData = options.body;
          const result = await DatabaseAPI.users.create({
            name: userData.name,
            email: userData.email,
            password: userData.password,
            phone: userData.phone,
            role: userData.role,
          });

          return {
            data: result,
            error: null,
          };
        default:
          throw new Error(`Function ${functionName} not implemented`);
      }
    } catch (error: any) {
      return {
        data: null,
        error: { message: error.message || 'Function call failed' },
      };
    }
  }
}

export class MySQLClient {
  auth = new AuthClient();
  storage = new StorageClient();
  functions = new FunctionsClient();

  from<T = any>(table: string): TableClient<T> {
    return new TableClient<T>(table);
  }
}

// Create and export the client instance
export const mysqlClient = new MySQLClient();

// Export types for compatibility
export type { Profile, Category, Subcategory, Transaction, UserProfile };
