import { useQuery } from '@tanstack/react-query';
import { httpMethods } from '@/api/config';

interface TransactionStats {
  total_transactions: number;
  total_income: number;
  total_expenses: number;
  net_balance: number;
  income_count: number;
  expense_count: number;
  avg_income: number;
  avg_expense: number;
}

interface TransactionStatsResponse {
  summary: TransactionStats;
  monthly_breakdown: Array<{
    month: string;
    income: number;
    expenses: number;
    transaction_count: number;
  }>;
  category_breakdown: Array<{
    category_name: string;
    type: string;
    total_amount: number;
    transaction_count: number;
  }>;
}

interface UseTransactionStatsParams {
  start_date?: string;
  end_date?: string;
}

export const useTransactionStats = (params: UseTransactionStatsParams = {}) => {
  return useQuery({
    queryKey: ['transaction-stats', params],
    queryFn: async (): Promise<TransactionStatsResponse> => {
      const queryParams = new URLSearchParams();
      
      if (params.start_date) {
        queryParams.append('start_date', params.start_date);
      }
      if (params.end_date) {
        queryParams.append('end_date', params.end_date);
      }

      const url = `/transactions/stats/summary${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      const response = await httpMethods.get<TransactionStatsResponse>(url);
      
      if (!response.success || !response.data) {
        throw new Error('Failed to fetch transaction statistics');
      }
      
      return response.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};
