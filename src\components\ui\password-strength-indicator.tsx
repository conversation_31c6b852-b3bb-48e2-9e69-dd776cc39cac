/**
 * Password Strength Indicator Component
 *
 * A comprehensive password strength validation and visualization component.
 * Used in user registration, password reset, and profile editing forms.
 *
 * Features:
 * - Real-time password strength calculation (0-4 scale)
 * - Visual progress bar with color coding
 * - Detailed feedback in Portuguese and English
 * - Requirements checklist with icons
 * - Responsive design with Tailwind CSS
 *
 * Progress Bar Implementation:
 * - Uses a custom progress bar (not the Radix Progress component)
 * - Color-coded: Red (weak) → Orange (fair) → Green (strong)
 * - Smooth transitions with CSS animations
 * - Width calculated as percentage of strength score
 */
import React from 'react';
import { Check, X, Shield, ShieldAlert, ShieldCheck } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface PasswordStrength {
  score: number; // 0-4 (0: very weak, 1: weak, 2: fair, 3: good, 4: strong)
  level: 'Very Weak' | 'Weak' | 'Fair' | 'Good' | 'Strong';
  levelPt: 'Muito Fraca' | 'Fraca' | 'Razoável' | 'Boa' | 'Forte';
  feedback: string[];
  feedbackPt: string[];
  color: string;
  bgColor: string;
  textColor: string;
  icon: React.ReactNode;
}

// Password strength calculation function
export const calculatePasswordStrength = (password: string): PasswordStrength => {
  if (!password) {
    return {
      score: 0,
      level: 'Very Weak',
      levelPt: 'Muito Fraca',
      feedback: ['Password is required'],
      feedbackPt: ['Senha é obrigatória'],
      color: 'bg-red-500',
      bgColor: 'bg-red-50',
      textColor: 'text-red-700',
      icon: <X className="h-4 w-4" />
    };
  }

  let score = 0;
  const feedback: string[] = [];
  const feedbackPt: string[] = [];

  // Length check
  if (password.length >= 8) {
    score += 1;
  } else {
    feedback.push('At least 8 characters');
    feedbackPt.push('Pelo menos 8 caracteres');
  }

  // Lowercase check
  if (/[a-z]/.test(password)) {
    score += 1;
  } else {
    feedback.push('Include lowercase letters');
    feedbackPt.push('Incluir letras minúsculas');
  }

  // Uppercase check
  if (/[A-Z]/.test(password)) {
    score += 1;
  } else {
    feedback.push('Include uppercase letters');
    feedbackPt.push('Incluir letras maiúsculas');
  }

  // Number check
  if (/\d/.test(password)) {
    score += 1;
  } else {
    feedback.push('Include numbers');
    feedbackPt.push('Incluir números');
  }

  // Special character check
  if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    score += 1;
  } else {
    feedback.push('Include special characters');
    feedbackPt.push('Incluir caracteres especiais');
  }

  // Determine strength level
  let level: PasswordStrength['level'];
  let levelPt: PasswordStrength['levelPt'];
  let color: string;
  let bgColor: string;
  let textColor: string;
  let icon: React.ReactNode;

  if (score <= 1) {
    level = 'Very Weak';
    levelPt = 'Muito Fraca';
    color = 'bg-red-500';
    bgColor = 'bg-red-50';
    textColor = 'text-red-700';
    icon = <X className="h-4 w-4" />;
  } else if (score === 2) {
    level = 'Weak';
    levelPt = 'Fraca';
    color = 'bg-orange-500';
    bgColor = 'bg-orange-50';
    textColor = 'text-orange-700';
    icon = <ShieldAlert className="h-4 w-4" />;
  } else if (score === 3) {
    level = 'Fair';
    levelPt = 'Razoável';
    color = 'bg-yellow-500';
    bgColor = 'bg-yellow-50';
    textColor = 'text-yellow-700';
    icon = <Shield className="h-4 w-4" />;
  } else if (score === 4) {
    level = 'Good';
    levelPt = 'Boa';
    color = 'bg-blue-500';
    bgColor = 'bg-blue-50';
    textColor = 'text-blue-700';
    icon = <ShieldCheck className="h-4 w-4" />;
  } else {
    level = 'Strong';
    levelPt = 'Forte';
    color = 'bg-green-500';
    bgColor = 'bg-green-50';
    textColor = 'text-green-700';
    icon = <Check className="h-4 w-4" />;
  }

  return {
    score,
    level,
    levelPt,
    feedback,
    feedbackPt,
    color,
    bgColor,
    textColor,
    icon
  };
};

interface PasswordStrengthIndicatorProps {
  password: string;
  showFeedback?: boolean;
  className?: string;
  language?: 'en' | 'pt';
}

export const PasswordStrengthIndicator: React.FC<PasswordStrengthIndicatorProps> = ({
  password,
  showFeedback = true,
  className,
  language = 'pt'
}) => {
  const strength = calculatePasswordStrength(password);
  const progressWidth = password ? (strength.score / 5) * 100 : 0;

  return (
    <div className={cn("space-y-2", className)}>
      {/* Strength Bar */}
      <div className="space-y-1">
        <div className="flex items-center justify-between text-sm">
          <span className="text-muted-foreground">
            {language === 'pt' ? 'Força da senha:' : 'Password strength:'}
          </span>
          <div className={cn("flex items-center gap-1 px-2 py-1 rounded-md text-xs font-medium", strength.bgColor, strength.textColor)}>
            {strength.icon}
            <span>{language === 'pt' ? strength.levelPt : strength.level}</span>
          </div>
        </div>
        
        {/* Progress Bar */}
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className={cn("h-2 rounded-full transition-all duration-300", strength.color)}
            style={{ width: `${progressWidth}%` }}
          />
        </div>
      </div>

      {/* Requirements Checklist */}
      {showFeedback && password && (
        <div className="space-y-1">
          <p className="text-xs text-muted-foreground">
            {language === 'pt' ? 'Requisitos:' : 'Requirements:'}
          </p>
          <div className="grid grid-cols-1 gap-1 text-xs">
            <div className="flex items-center gap-2">
              {password.length >= 8 ? (
                <Check className="h-3 w-3 text-green-500" />
              ) : (
                <X className="h-3 w-3 text-red-500" />
              )}
              <span className={password.length >= 8 ? 'text-green-600' : 'text-red-600'}>
                {language === 'pt' ? 'Pelo menos 8 caracteres' : 'At least 8 characters'}
              </span>
            </div>
            <div className="flex items-center gap-2">
              {/[a-z]/.test(password) ? (
                <Check className="h-3 w-3 text-green-500" />
              ) : (
                <X className="h-3 w-3 text-red-500" />
              )}
              <span className={/[a-z]/.test(password) ? 'text-green-600' : 'text-red-600'}>
                {language === 'pt' ? 'Letras minúsculas' : 'Lowercase letters'}
              </span>
            </div>
            <div className="flex items-center gap-2">
              {/[A-Z]/.test(password) ? (
                <Check className="h-3 w-3 text-green-500" />
              ) : (
                <X className="h-3 w-3 text-red-500" />
              )}
              <span className={/[A-Z]/.test(password) ? 'text-green-600' : 'text-red-600'}>
                {language === 'pt' ? 'Letras maiúsculas' : 'Uppercase letters'}
              </span>
            </div>
            <div className="flex items-center gap-2">
              {/\d/.test(password) ? (
                <Check className="h-3 w-3 text-green-500" />
              ) : (
                <X className="h-3 w-3 text-red-500" />
              )}
              <span className={/\d/.test(password) ? 'text-green-600' : 'text-red-600'}>
                {language === 'pt' ? 'Números' : 'Numbers'}
              </span>
            </div>
            <div className="flex items-center gap-2">
              {/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password) ? (
                <Check className="h-3 w-3 text-green-500" />
              ) : (
                <X className="h-3 w-3 text-red-500" />
              )}
              <span className={/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password) ? 'text-green-600' : 'text-red-600'}>
                {language === 'pt' ? 'Caracteres especiais' : 'Special characters'}
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
