
import { But<PERSON> } from "@/components/ui/button";
import { DataTable, Column } from "@/components/DataTable";
import { Pencil, Trash2, Plus } from "lucide-react";
import { Category } from "./CategoryTable";

export interface Subcategory {
  id: string;
  name: string;
  category_id: string | null;
  category_name?: string;
}

interface SubcategoryTableProps {
  subcategories: Subcategory[];
  categories: Category[];
  isLoading: boolean;
  handleNewSubcategory: () => void;
  handleEditSubcategory: (subcategory: Subcategory) => void;
  handleDeleteSubcategory: (subcategory: Subcategory) => void;
}

export function SubcategoryTable({ 
  subcategories, 
  categories,
  isLoading, 
  handleNewSubcategory, 
  handleEditSubcategory, 
  handleDeleteSubcategory 
}: SubcategoryTableProps) {
  
  const subcategoryColumns: Column<Subcategory>[] = [
    {
      header: "Nome",
      accessorKey: "name",
    },
    {
      header: "Categoria",
      accessorKey: "category_name",
      className: "text-center",
    },
    {
      header: "Ações",
      accessorKey: (row: Subcategory) => (
        <div className="flex justify-center space-x-2">
          <Button variant="outline" size="icon" className="h-8 w-8" onClick={() => handleEditSubcategory(row)}>
            <Pencil className="h-4 w-4" />
          </Button>
          <Button variant="outline" size="icon" className="h-8 w-8 text-red-600" onClick={() => handleDeleteSubcategory(row)}>
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      ),
      className: "w-[100px] text-center",
    },
  ];

  return (
    <div className="bg-white p-6 rounded-lg border shadow-sm">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-lg font-medium">Gerenciar Subcategorias</h2>
        <Button 
          className="bg-twtwins-purple hover:bg-twtwins-dark-purple"
          onClick={handleNewSubcategory}
          disabled={categories.length === 0}
        >
          <Plus size={16} className="mr-2" />
          Nova Subcategoria
        </Button>
      </div>
      
      <DataTable
        columns={subcategoryColumns}
        data={subcategories}
        emptyMessage="Nenhuma subcategoria encontrada."
        loading={isLoading}
      />
    </div>
  );
}
