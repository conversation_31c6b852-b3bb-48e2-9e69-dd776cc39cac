
import StatCard from "@/components/StatCard";
import { FileText, TrendingUp, BarChart } from "lucide-react";
import { formatCurrency } from "@/lib/formatters";
import { Transaction } from "@/hooks/useTransactions";
import { useTransactionStats } from "@/hooks/useTransactionStats";

interface PaginationData {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

interface StatisticsSectionProps {
  transactions: Transaction[] | undefined;
  pagination: PaginationData;
}

const StatisticsSection = ({ transactions, pagination }: StatisticsSectionProps) => {
  // Buscar estatísticas usando endpoint específico
  const { data: statsData, isLoading: statsLoading } = useTransactionStats();

  // Use dados das estatísticas se disponíveis, senão use paginação para total
  const totalTransactions = statsData?.summary?.total_transactions || pagination.total || 0;
  const totalEntradas = statsData?.summary?.total_income || 0;
  const totalSaidas = statsData?.summary?.total_expenses || 0;
  const saldo = statsData?.summary?.net_balance || (totalEntradas - totalSaidas);

  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
      <StatCard
        title="Total de Transações"
        value={totalTransactions.toString()}
        variant="purple"
        icon={<FileText className="h-5 w-5 text-javiagens-red" />}
      />
      <StatCard
        title="Total de Entradas"
        value={formatCurrency(totalEntradas)}
        variant="green"
        icon={<TrendingUp className="h-5 w-5 text-javiagens-green" />}
      />
      <StatCard
        title="Total de Saídas"
        value={formatCurrency(totalSaidas)}
        variant="red"
        icon={<TrendingUp className="h-5 w-5 text-javiagens-red rotate-180" />}
      />
      <StatCard
        title="Saldo Atual"
        value={formatCurrency(saldo)}
        variant="blue"
        icon={<BarChart className="h-5 w-5 text-javiagens-blue" />}
      />
    </div>
  );
};

export default StatisticsSection;
