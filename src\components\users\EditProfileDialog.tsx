import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Loader2 } from "lucide-react";
import { DatabaseAPI } from "@/api/database";
import { toastUtils } from "@/lib/toast-utils";
import { useMutation, useQueryClient } from "@tanstack/react-query";

interface EditProfileDialogProps {
  isOpen: boolean;
  onClose: () => void;
  userId: string;
  userName: string;
  onSuccess?: (newName: string) => void;
}

export const EditProfileDialog = ({ isOpen, onClose, userId, userName, onSuccess }: EditProfileDialogProps) => {
  const [name, setName] = useState(userName);
  const queryClient = useQueryClient();

  // Mutação para atualizar perfil do usuário
  const updateProfileMutation = useMutation({
   mutationFn: async () => {
     try {
       await DatabaseAPI.profiles.update(userId, { name });
       return name;
     } catch (error: any) {
       throw new Error(error.message || 'Erro ao atualizar perfil');
     }
   },
   onSuccess: (newName) => {
     toastUtils.success("Perfil atualizado com sucesso!");
     if (onSuccess) onSuccess(newName);
     onClose();
   },
   onError: (error) => {
     console.error(error);
     toastUtils.error("Erro ao atualizar perfil", error.message);
   }
 });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    updateProfileMutation.mutate();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
   <DialogContent className="sm:max-w-[425px]">
     <DialogHeader>
       <DialogTitle>Editar Perfil</DialogTitle>
       <DialogDescription>
         Atualize suas informações de perfil.
       </DialogDescription>
     </DialogHeader>

     <form onSubmit={handleSubmit} className="space-y-4 py-4">
       <div className="space-y-2">
         <label className="text-sm font-medium">Nome</label>
         <Input
           value={name}
           onChange={(e) => setName(e.target.value)}
           placeholder="Seu nome completo"
           required
         />
       </div>

       <DialogFooter>
         <Button
           type="button"
           variant="outline"
           onClick={onClose}
         >
           Cancelar
         </Button>
         <Button
           type="submit"
           className="bg-twtwins-purple hover:bg-twtwins-dark-purple"
           disabled={updateProfileMutation.isPending}
         >
           {updateProfileMutation.isPending ? (
             <>
               <Loader2 className="mr-2 h-4 w-4 animate-spin" />
               Salvando...
             </>
           ) : (
             "Salvar"
           )}
         </Button>
       </DialogFooter>
     </form>
   </DialogContent>
 </Dialog>
 );
};
